import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 模拟数据库
const mockDb = {
  abortList: (() => {
    const result = [];
    const statusOptions = [0, 1]; // 是、否
    const rentTypeOptions = [0, 1, 2, 3]; // 出租方式
    
    // 企业名称数组
    const enterpriseOptions = [
      '厦门市城市建设发展投资有限公司',
      '厦门市地热资源管理有限公司',
      '厦门兴地房屋征迁服务有限公司',
      '厦门地丰置业有限公司',
      '图智策划咨询（厦门）有限公司',
      '厦门市集众祥和物业管理有限公司',
      '厦门市人居乐业物业服务有限公司'
    ];
    
    // 创建人名称数组
    const entryClerks = ["张明", "李华", "王芳", "赵刚", "钱伟"];
    const operators = ["经办人A", "经办人B", "经办人C"];
    
    // 中止原因数组
    const abortReasons = [
      '租户违约，未按约定支付租金',
      '租户单方面要求终止租赁合同',
      '双方协商一致终止合同',
      '资产需进行维修改造，无法继续租赁',
      '政策变动导致租赁条件发生变化',
      '因公共安全原因终止租赁',
      '租户破产倒闭，无法继续履行合同'
    ];
    
    // 标的名称数组
    const targetNames = [
      '厦门市思明区某商业建筑',
      '厦门市湖里区某办公楼',
      '厦门市集美区某商铺',
      '厦门市海沧区某仓库',
      '厦门市同安区某工业厂房'
    ];

    for (let i = 0; i < 48; i++) {
      // 随机生成是否成交
      const abortStatus = statusOptions[Math.floor(Math.random() * statusOptions.length)];
      
      // 随机生成出租方式
      const rentType = rentTypeOptions[Math.floor(Math.random() * rentTypeOptions.length)];
      
      // 随机总租金（只有成交时才有）
      const actTotalRent = abortStatus === 1 ? (Math.random() * 300 + 50).toFixed(2) : null;
      
      // 随机中止原因（只有成交时才有）
      const abortReason = abortStatus === 1 ? abortReasons[Math.floor(Math.random() * abortReasons.length)] : '';
      
      // 随机标的名称（只有成交时才有）
      const targetName = abortStatus === 1 ? targetNames[Math.floor(Math.random() * targetNames.length)] : '';
      
      // 随机所属企业
      const manageUnit = Math.floor(Math.random() * enterpriseOptions.length);
      
      // 随机创建时间
      const createDate = Random.datetime('yyyy-MM-dd HH:mm:ss');
      // 更新时间晚于创建时间
      const updateDate = Random.datetime('yyyy-MM-dd HH:mm:ss');
      
      // 随机创建人
      const entryClerk = entryClerks[Math.floor(Math.random() * entryClerks.length)];
      const operator = operators[Math.floor(Math.random() * operators.length)];
      
      // 生成中止日期（只有成交时才有）
      const abortDate = abortStatus === 1 ? Random.date('yyyy-MM-dd') : '';

      result.push({
        id: 30000 + i,
        name: `租赁资产包${i + 1}`,
        code: `ZL${String(30000 + i).padStart(6, '0')}`,
        manageUnit: manageUnit,
        reportOrNot: Random.integer(0, 1),
        operator: operator,
        rentType: rentType,
        abortStatus: abortStatus,
        targetName: targetName,
        abortDate: abortDate,
        actTotalRent: actTotalRent,
        abortReason: abortReason,
        entryClerk: entryClerk,
        createTime: createDate,
        updateTime: updateDate
      });
    }
    
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/abort/list`,
    method: 'get',
    response: ({ query }) => {
      const { 
        pageNo = 1, 
        pageSize = 10, 
        name, 
        code, 
        abortStatus, 
        rentType, 
        manageUnit,
        reportOrNot,
        operator,
        targetName,
        abortDateRange,
        minActTotalRent,
        entryClerk,
        createTime,
        updateTime
      } = query;
      
      let list = [...mockDb.abortList];
      
      // 条件筛选
      if (name) {
        list = list.filter(item => item.name.includes(name));
      }
      
      if (code) {
        list = list.filter(item => item.code.includes(code));
      }
      
      if (abortStatus !== undefined && abortStatus !== null && abortStatus !== '') {
        list = list.filter(item => item.abortStatus === parseInt(abortStatus));
      }
      
      if (rentType !== undefined && rentType !== null && rentType !== '') {
        list = list.filter(item => item.rentType === parseInt(rentType));
      }
      
      if (manageUnit !== undefined && manageUnit !== null && manageUnit !== '') {
        list = list.filter(item => item.manageUnit === parseInt(manageUnit));
      }
      
      if (reportOrNot !== undefined && reportOrNot !== null && reportOrNot !== '') {
        list = list.filter(item => item.reportOrNot === parseInt(reportOrNot));
      }
      
      if (operator) {
        list = list.filter(item => item.operator.includes(operator));
      }
      
      if (targetName) {
        list = list.filter(item => item.targetName && item.targetName.includes(targetName));
      }
      
      if (minActTotalRent) {
        list = list.filter(item => item.actTotalRent && parseFloat(item.actTotalRent) >= parseFloat(minActTotalRent));
      }
      
      if (entryClerk) {
        list = list.filter(item => item.entryClerk.includes(entryClerk));
      }
      
      // 日期范围筛选
      if (abortDateRange && abortDateRange.length === 2) {
        const [startDate, endDate] = abortDateRange;
        list = list.filter(item => {
          if (!item.abortDate) return false;
          return item.abortDate >= startDate && item.abortDate <= endDate;
        });
      }
      
      if (createTime && createTime.length === 2) {
        const [startDate, endDate] = createTime;
        list = list.filter(item => {
          const itemDate = item.createTime.split(' ')[0];
          return itemDate >= startDate && itemDate <= endDate;
        });
      }
      
      if (updateTime && updateTime.length === 2) {
        const [startDate, endDate] = updateTime;
        list = list.filter(item => {
          const itemDate = item.updateTime.split(' ')[0];
          return itemDate >= startDate && itemDate <= endDate;
        });
      }
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 获取详情
  {
    url: `${baseUrl}/abort/detail`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      
      // 查找对应ID的记录
      const item = mockDb.abortList.find(item => item.id === parseInt(id));
      
      if (!item) {
        return resultError('未找到对应记录');
      }
      
      return resultSuccess(item);
    },
  },
  
  // 保存
  {
    url: `${baseUrl}/abort/add`,
    method: 'post',
    response: ({ body }) => {
      const id = mockDb.abortList.length + 30000;
      const code = `ZL${String(id).padStart(6, '0')}`;
      
      const newItem = {
        ...body,
        id,
        code,
        createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      };
      
      mockDb.abortList.push(newItem);
      
      return resultSuccess({
        id,
        code,
      });
    },
  },
  
  // 更新
  {
    url: `${baseUrl}/abort/edit`,
    method: 'post',
    response: ({ body }) => {
      const { id } = body;
      
      const index = mockDb.abortList.findIndex(item => item.id === parseInt(id));
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.abortList[index] = {
        ...mockDb.abortList[index],
        ...body,
        updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      };
      
      return resultSuccess(true);
    },
  },
  
  // 删除
  {
    url: `${baseUrl}/abort/delete`,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      
      const index = mockDb.abortList.findIndex(item => item.id === parseInt(id));
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.abortList.splice(index, 1);
      
      return resultSuccess(true);
    },
  },
  
  // 批量删除
  {
    url: `${baseUrl}/abort/deleteBatch`,
    method: 'delete',
    response: ({ body }) => {
      const { ids } = body;
      
      if (!ids || !Array.isArray(ids)) {
        return resultError('参数错误');
      }
      
      ids.forEach(id => {
        const index = mockDb.abortList.findIndex(item => item.id === parseInt(id));
        if (index !== -1) {
          mockDb.abortList.splice(index, 1);
        }
      });
      
      return resultSuccess(true);
    },
  },
  
  // 导入
  {
    url: `${baseUrl}/abort/importExcel`,
    method: 'post',
    response: () => {
      // 模拟导入成功
      return resultSuccess({
        successCount: 8,
        failCount: 0,
        message: '导入成功'
      });
    },
  },
  
  // 导出
  {
    url: `${baseUrl}/abort/exportXls`,
    method: 'get',
    response: () => {
      // 模拟导出成功
      return resultSuccess('导出成功');
    },
  },
  
  // 全部导出
  {
    url: `${baseUrl}/abort/exportAll`,
    method: 'get',
    response: () => {
      // 模拟导出成功
      return resultSuccess('全部导出成功');
    },
  },
  
  // 下载模板
  {
    url: `${baseUrl}/abort/downloadTemplate`,
    method: 'get',
    response: () => {
      // 模拟下载模板成功
      return resultSuccess('模板下载成功');
    },
  },
] as MockMethod[];
