import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 广告位资产名称列表
const adNames = [
  '户外广告牌',
  'LED显示屏',
  '公交站台广告',
  '地铁站广告',
  '商场广告位',
  '写字楼广告',
  '高速公路广告',
  '机场广告位',
  '火车站广告',
  '码头广告位',
  '公园广告牌',
  '体育场馆广告',
  '医院广告位',
  '学校广告位',
  '社区广告牌',
  '商业街广告',
  '广场广告位',
  '桥梁广告牌',
  '隧道广告位',
  '停车场广告',
];

// 管理单位列表
const _manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
];

// 备注列表
const remarks = [
  '广告位运行正常',
  '需要定期维护',
  '建议更新换代',
  '广告位老化严重',
  '效果良好',
  '需要维修',
  '闲置状态',
  '出租使用中',
  '自用广告位',
  '出借给其他部门',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成广告位资产mock数据
const createAdData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const companyName = i % 7; // 0-6 对应不同的企业
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const approvalStatus = i % 2; // 0-是, 1-否
    const entryStatus = i % 2; // 0-是, 1-否

    // 生成资产使用状态（可能包含多个状态）
    const assetsStatusOptions = [
      [0], [1], [2], [3], [4], [5], [6], [7], [0, 1], [2, 5], [1, 2], [0, 3]
    ];
    const assetsStatus = assetsStatusOptions[i % assetsStatusOptions.length];

    // 根据广告位类型生成不同的金额和面积
    let adArea = 0;
    let assetsAmount = 0;
    let bookAmount = 0;
    let adQuantity = 0;

    switch (i % 5) {
      case 0: // 户外广告牌
        adArea = Number((50 + Math.random() * 100).toFixed(2));
        assetsAmount = Number((50000 + Math.random() * 150000).toFixed(2));
        adQuantity = Math.floor(Math.random() * 3) + 1;
        break;
      case 1: // LED显示屏
        adArea = Number((20 + Math.random() * 50).toFixed(2));
        assetsAmount = Number((100000 + Math.random() * 300000).toFixed(2));
        adQuantity = Math.floor(Math.random() * 2) + 1;
        break;
      case 2: // 公交站台广告
        adArea = Number((10 + Math.random() * 30).toFixed(2));
        assetsAmount = Number((20000 + Math.random() * 80000).toFixed(2));
        adQuantity = Math.floor(Math.random() * 5) + 1;
        break;
      case 3: // 地铁站广告
        adArea = Number((30 + Math.random() * 80).toFixed(2));
        assetsAmount = Number((80000 + Math.random() * 200000).toFixed(2));
        adQuantity = Math.floor(Math.random() * 4) + 1;
        break;
      case 4: // 其他广告位
        adArea = Number((15 + Math.random() * 60).toFixed(2));
        assetsAmount = Number((30000 + Math.random() * 120000).toFixed(2));
        adQuantity = Math.floor(Math.random() * 3) + 1;
        break;
    }

    // 计算账面价值（假设折旧20%）
    bookAmount = Number((assetsAmount * 0.8).toFixed(2));

    // 生成资产编号
    const companyCode = '0016';
    const now = new Date();
    const yearMonth = now.getFullYear() + ('0' + (now.getMonth() + 1)).slice(-2);
    const serial = i.toString().padStart(5, '0');
    const assetCode = 'GG' + companyCode + yearMonth + serial;

    // 生成日期
    const createDate = generateRandomDate(new Date(2022, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());
    const setDate = generateRandomDate(new Date(2020, 0, 1), createDate);
    const bookValueDate = generateRandomDate(setDate, new Date());

    data.push({
      id: i,
      code: assetCode,
      enterpriseCode: 'E' + (20000 + i),
      name: adNames[i % adNames.length] + (i + 1),
      groupName: 0, // 集团固定为厦门市城市建设发展投资有限公司
      companyName: companyName,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      province: '福建省',
      city: '厦门市',
      area: ['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'][i % 6],
      address: '厦门市某某路' + (i + 1) + '号',
      status: status,
      adArea: adArea,
      assetsStatus: assetsStatus,
      setDate: formatDate(setDate),
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(bookValueDate),
      remainingUsefulYears: Math.floor(Math.random() * 10) + 1 + '年',
      adQuantity: adQuantity,
      approvalStatus: approvalStatus,
      entryStatus: entryStatus,
      adOrg: '市场部',
      adContacts: '联系人' + (i + 1),
      contactsPhone: '1391234' + (10000 + i).toString().substring(1),
      remark: Math.random() > 0.7 ? remarks[i % remarks.length] : '',
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: formatDate(createDate),
      updateTime: formatDate(updateDate),
    });
  }

  return data;
};

const adData = createAdData();

export default [
  {
    url: `${baseUrl}/ad/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      const _start = (page - 1) * pageSize;
      const _end = _start + pageSize;
      let filteredData = [...adData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.includes(params.name));
      }
      if (params.code) {
        filteredData = filteredData.filter((item) => item.code.includes(params.code));
      }
      if (params.enterpriseCode) {
        filteredData = filteredData.filter((item) => item.enterpriseCode.includes(params.enterpriseCode));
      }
      if (params.companyName !== undefined && params.companyName !== '') {
        filteredData = filteredData.filter((item) => item.companyName === Number(params.companyName));
      }
      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter((item) => item.status === Number(params.status));
      }
      if (params.manageUnit !== undefined && params.manageUnit !== '') {
        filteredData = filteredData.filter((item) => item.manageUnit === Number(params.manageUnit));
      }
      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter((item) => item.reportOrNot === Number(params.reportOrNot));
      }
      if (params.approvalStatus !== undefined && params.approvalStatus !== '') {
        filteredData = filteredData.filter((item) => item.approvalStatus === Number(params.approvalStatus));
      }
      if (params.entryStatus !== undefined && params.entryStatus !== '') {
        filteredData = filteredData.filter((item) => item.entryStatus === Number(params.entryStatus));
      }
      if (params.operator) {
        filteredData = filteredData.filter((item) => item.operator.includes(params.operator));
      }
      if (params.entryClerk) {
        filteredData = filteredData.filter((item) => item.entryClerk.includes(params.entryClerk));
      }
      if (params.adAreaMin) {
        filteredData = filteredData.filter((item) => item.adArea >= Number(params.adAreaMin));
      }
      if (params.adAreaMax) {
        filteredData = filteredData.filter((item) => item.adArea <= Number(params.adAreaMax));
      }
      if (params.assetsAmountMin) {
        filteredData = filteredData.filter((item) => item.assetsAmount >= Number(params.assetsAmountMin));
      }
      if (params.assetsAmountMax) {
        filteredData = filteredData.filter((item) => item.assetsAmount <= Number(params.assetsAmountMax));
      }
      if (params.bookAmountMin) {
        filteredData = filteredData.filter((item) => item.bookAmount >= Number(params.bookAmountMin));
      }
      if (params.bookAmountMax) {
        filteredData = filteredData.filter((item) => item.bookAmount <= Number(params.bookAmountMax));
      }

      return resultPageSuccess(page, pageSize, filteredData);
    },
  },
  {
    url: `${baseUrl}/ad/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = adData.length + 1;
      const newAd = {
        id: newId,
        ...body,
        createTime: formatDate(new Date()),
        updateTime: formatDate(new Date()),
      };
      adData.push(newAd);
      return resultSuccess(newAd);
    },
  },
  {
    url: `${baseUrl}/ad/edit`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const index = adData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        adData[index] = {
          ...adData[index],
          ...body,
          updateTime: formatDate(new Date()),
        };
        return resultSuccess(adData[index]);
      }
      return resultError('广告位不存在');
    },
  },
  {
    url: `${baseUrl}/ad/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const index = adData.findIndex((item) => item.id === Number(query.id));
      if (index !== -1) {
        adData.splice(index, 1);
        return resultSuccess('删除成功');
      }
      return resultError('广告位不存在');
    },
  },
  {
    url: `${baseUrl}/ad/deleteBatch`,
    timeout: 200,
    method: 'delete',
    response: ({ body }) => {
      const ids = body.ids || [];
      ids.forEach((id) => {
        const index = adData.findIndex((item) => item.id === id);
        if (index !== -1) {
          adData.splice(index, 1);
        }
      });
      return resultSuccess('批量删除成功');
    },
  },
  {
    url: `${baseUrl}/ad/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const ad = adData.find((item) => item.id === Number(query.id));
      if (ad) {
        return resultSuccess(ad);
      }
      return resultError('广告位不存在');
    },
  },
  {
    url: `${baseUrl}/ad/importExcel`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess('导入成功');
    },
  },
  {
    url: `${baseUrl}/ad/exportXls`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('导出成功');
    },
  },
  {
    url: `${baseUrl}/ad/exportAll`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('全部导出成功');
    },
  },
  {
    url: `${baseUrl}/ad/downloadTemplate`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('模板下载成功');
    },
  },
] as MockMethod[]; 