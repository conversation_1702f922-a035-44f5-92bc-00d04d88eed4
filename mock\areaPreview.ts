import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';

// 模拟面积数据
const mockAreaData = [
  {
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    totalArea: 56789.45,
    propertyRightArea: 56789.45,
    nonPropertyRightArea: 0,
    rentableArea: 45000.0,
    rentedArea: 38600.23,
    idleArea: 6399.77,
    idleAreaRatio: 14.22,
    rentRatio: 85.78,
    professionalArea: 25600.0,
    nonProfessionalArea: 13000.23,
    xiamenPublicRentArea: 10500.0,
    otherPlacePublicRentArea: 6200.0,
    nonEntryPublicRentArea: 5400.0,
    otherRentMethodArea: 3500.23,
    temporaryIdleArea: 1500.0,
    idleOver6MonthsArea: 4899.77,
    idleOver6MonthsRatio: 8.63,
    selfUsedArea: 7500.0,
    occupiedArea: 3200.0,
    borrowedArea: 1500.0,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    totalArea: 32450.78,
    propertyRightArea: 0,
    nonPropertyRightArea: 32450.78,
    rentableArea: 30000.0,
    rentedArea: 18450.34,
    idleArea: 11549.66,
    idleAreaRatio: 38.50,
    rentRatio: 61.50,
    professionalArea: 12000.0,
    nonProfessionalArea: 6450.34,
    xiamenPublicRentArea: 7200.0,
    otherPlacePublicRentArea: 2500.0,
    nonEntryPublicRentArea: 1800.0,
    otherRentMethodArea: 950.34,
    temporaryIdleArea: 2500.0,
    idleOver6MonthsArea: 9049.66,
    idleOver6MonthsRatio: 27.89,
    selfUsedArea: 1500.0,
    occupiedArea: 800.0,
    borrowedArea: 150.0,
    companyName: '厦门市地热资源管理有限公司'
  },
  {
    assetType: '土地',
    propertyRight: 'agent',
    propertyRightText: '代管',
    totalArea: 12800.0,
    propertyRightArea: 0,
    nonPropertyRightArea: 12800.0,
    rentableArea: 12800.0,
    rentedArea: 9600.0,
    idleArea: 3200.0,
    idleAreaRatio: 25.0,
    rentRatio: 75.0,
    professionalArea: 5000.0,
    nonProfessionalArea: 4600.0,
    xiamenPublicRentArea: 3000.0,
    otherPlacePublicRentArea: 1500.0,
    nonEntryPublicRentArea: 1200.0,
    otherRentMethodArea: 900.0,
    temporaryIdleArea: 1200.0,
    idleOver6MonthsArea: 2000.0,
    idleOver6MonthsRatio: 15.63,
    selfUsedArea: 0,
    occupiedArea: 0,
    borrowedArea: 0,
    companyName: '厦门兴地房屋征迁服务有限公司'
  },
  {
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    totalArea: 28950.65,
    propertyRightArea: 28950.65,
    nonPropertyRightArea: 0,
    rentableArea: 23500.0,
    rentedArea: 20120.45,
    idleArea: 3379.55,
    idleAreaRatio: 14.38,
    rentRatio: 85.62,
    professionalArea: 12000.0,
    nonProfessionalArea: 8120.45,
    xiamenPublicRentArea: 7500.0,
    otherPlacePublicRentArea: 2800.0,
    nonEntryPublicRentArea: 1750.0,
    otherRentMethodArea: 70.45,
    temporaryIdleArea: 1200.0,
    idleOver6MonthsArea: 2179.55,
    idleOver6MonthsRatio: 7.53,
    usableArea: 25600.0,
    selfUsedArea: 2100.0,
    occupiedArea: 1350.0,
    borrowedArea: 650.0,
    companyName: '厦门地丰置业有限公司'
  },
  {
    assetType: '房屋',
    propertyRight: 'no',
    propertyRightText: '无产权',
    totalArea: 15670.32,
    propertyRightArea: 0,
    nonPropertyRightArea: 15670.32,
    rentableArea: 14000.0,
    rentedArea: 8950.62,
    idleArea: 5049.38,
    idleAreaRatio: 36.07,
    rentRatio: 63.93,
    professionalArea: 5200.0,
    nonProfessionalArea: 3750.62,
    xiamenPublicRentArea: 3000.0,
    otherPlacePublicRentArea: 1400.0,
    nonEntryPublicRentArea: 800.0,
    otherRentMethodArea: 0,
    temporaryIdleArea: 1800.0,
    idleOver6MonthsArea: 3249.38,
    idleOver6MonthsRatio: 20.74,
    usableArea: 14500.0,
    selfUsedArea: 500.0,
    occupiedArea: 200.0,
    borrowedArea: 300.0,
    companyName: '图智策划咨询（厦门）有限公司'
  },
  {
    assetType: '房屋',
    propertyRight: 'agent',
    propertyRightText: '代管',
    totalArea: 8760.0,
    propertyRightArea: 0,
    nonPropertyRightArea: 8760.0,
    rentableArea: 8500.0,
    rentedArea: 7225.0,
    idleArea: 1275.0,
    idleAreaRatio: 15.0,
    rentRatio: 85.0,
    professionalArea: 4500.0,
    nonProfessionalArea: 2725.0,
    xiamenPublicRentArea: 2800.0,
    otherPlacePublicRentArea: 1200.0,
    nonEntryPublicRentArea: 500.0,
    otherRentMethodArea: 0,
    temporaryIdleArea: 600.0,
    idleOver6MonthsArea: 675.0,
    idleOver6MonthsRatio: 7.71,
    usableArea: 8500.0,
    selfUsedArea: 0,
    occupiedArea: 0,
    borrowedArea: 0,
    companyName: '厦门市集众祥和物业管理有限公司'
  }
];

export default [
  // 获取面积一览表数据
  {
    url: `${baseUrl}/areaPreview/list`,
    method: 'get',
    response: ({ query }) => {
      const { assetType, propertyRight, companies } = query;
      
      let list = [...mockAreaData];
      
      // 条件筛选
      if (assetType) {
        const assetTypeText = assetType === 'land' ? '土地' : '房屋';
        list = list.filter(item => item.assetType === assetTypeText);
      }
      
      if (propertyRight) {
        list = list.filter(item => item.propertyRight === propertyRight);
      }
      
      if (companies && companies.length > 0) {
        const companiesArray = Array.isArray(companies) ? companies : [companies];
        list = list.filter(item => item.companyName && companiesArray.includes(item.companyName));
      }
      
      return resultSuccess(list);
    },
  },
  
  // 导出面积一览表数据
  {
    url: `${baseUrl}/areaPreview/export`,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出功能，实际应该返回文件流
      return resultSuccess('导出成功');
    },
  },
] as MockMethod[];
