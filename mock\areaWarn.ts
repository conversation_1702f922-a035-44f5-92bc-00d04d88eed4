import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl, pagination } from './_util';
import { Random } from 'mockjs';

// 模拟面积统计预警数据
const mockAreaWarnData = [
  {
    companyName: '厦门市城市建设发展投资有限公司',
    assetType: '土地',
    assetCode: 'TD00QGJT2022122183412344',
    assetName: '土地资产-1001',
    warningContent: 'TD00QGJT2022122183412344资产使用状态为[自用],各状态面积统计(㎡)为[闲置:1.00、自用:1.00、出租:1.00、出借:1.00、占用:1.00、转让:1.00],状态与面积信息不匹配.\nTD00QGJT2022122183412344资产为[自用]使用状态,各状态面积统计(㎡)为[闲置1.00、自用1.00、出租1.00、出借1.00、占用1.00、转让1.00],各记录中[闲置0.00、自用0.00、出租：0.00、出借0.00、占用0.00、转让0.00],数值不匹配'
  },
  {
    companyName: '厦门市地热资源管理有限公司',
    assetType: '房屋',
    assetCode: 'FW00QGJT2022122183412355',
    assetName: '房屋资产-2001',
    warningContent: 'FW00QGJT2022122183412355资产使用状态为[出租],各状态面积统计(㎡)为[闲置:0.00、自用:0.00、出租:50.00、出借:20.00、占用:0.00、转让:0.00],状态与面积信息不匹配'
  },
  {
    companyName: '厦门兴地房屋征迁服务有限公司',
    assetType: '土地',
    assetCode: 'TD00QGJT2022122183412366',
    assetName: '土地资产-3001',
    warningContent: 'TD00QGJT2022122183412366资产为[闲置]使用状态,各状态面积统计(㎡)为[闲置150.00、自用0.00、出租0.00、出借0.00、占用0.00、转让0.00],各记录中[闲置100.00、自用0.00、出租：0.00、出借0.00、占用0.00、转让0.00],数值不匹配'
  },
  {
    companyName: '厦门地丰置业有限公司',
    assetType: '房屋',
    assetCode: 'FW00QGJT2022122183412377',
    assetName: '房屋资产-4001',
    warningContent: 'FW00QGJT2022122183412377资产使用状态为[出借],各状态面积统计(㎡)为[闲置:0.00、自用:30.00、出租:0.00、出借:70.00、占用:0.00、转让:0.00],状态与面积信息不匹配'
  },
  {
    companyName: '图智策划咨询（厦门）有限公司',
    assetType: '土地',
    assetCode: 'TD00QGJT2022122183412388',
    assetName: '土地资产-5001',
    warningContent: 'TD00QGJT2022122183412388资产使用状态为[占用],各状态面积统计(㎡)为[闲置:0.00、自用:0.00、出租:0.00、出借:0.00、占用:200.00、转让:50.00],状态与面积信息不匹配'
  },
  {
    companyName: '厦门市集众祥和物业管理有限公司',
    assetType: '房屋',
    assetCode: 'FW00QGJT2022122183412399',
    assetName: '房屋资产-6001',
    warningContent: 'FW00QGJT2022122183412399资产使用状态为[转让],各状态面积统计(㎡)为[闲置:10.00、自用:0.00、出租:0.00、出借:0.00、占用:0.00、转让:80.00],状态与面积信息不匹配'
  },
  {
    companyName: '厦门市人居乐业物业服务有限公司',
    assetType: '土地',
    assetCode: 'TD00QGJT2022122183412400',
    assetName: '土地资产-7001',
    warningContent: 'TD00QGJT2022122183412400资产为[自用]使用状态,各状态面积统计(㎡)为[闲置0.00、自用120.00、出租0.00、出借0.00、占用0.00、转让0.00],各记录中[闲置0.00、自用100.00、出租：0.00、出借0.00、占用0.00、转让0.00],数值不匹配'
  }
];

// 生成更多模拟数据
const generateMoreMockData = () => {
  const companies = [
    '厦门市城市建设发展投资有限公司',
    '厦门市地热资源管理有限公司', 
    '厦门兴地房屋征迁服务有限公司',
    '厦门地丰置业有限公司',
    '图智策划咨询（厦门）有限公司',
    '厦门市集众祥和物业管理有限公司',
    '厦门市人居乐业物业服务有限公司'
  ];
  
  const assetTypes = ['土地', '房屋'];
  const statuses = ['自用', '出租', '出借', '闲置', '占用', '转让'];
  
  const warningTemplates = [
    '{code}资产使用状态为[{status}],各状态面积统计(㎡)为[闲置:{area1}、自用:{area2}、出租:{area3}、出借:{area4}、占用:{area5}、转让:{area6}],状态与面积信息不匹配',
    '{code}资产为[{status}]使用状态,各状态面积统计(㎡)为[闲置{area1}、自用{area2}、出租{area3}、出借{area4}、占用{area5}、转让{area6}],各记录中[闲置{rarea1}、自用{rarea2}、出租：{rarea3}、出借{rarea4}、占用{rarea5}、转让{rarea6}],数值不匹配'
  ];
  
  const additionalData = [];
  
  for (let i = 8; i <= 100; i++) {
    const assetType = assetTypes[Math.floor(Math.random() * assetTypes.length)];
    const assetPrefix = assetType === '土地' ? 'TD' : 'FW';
    const assetCode = `${assetPrefix}00QGJT${Math.floor(10000000000000 + Math.random() * 90000000000000)}`;
    const company = companies[Math.floor(Math.random() * companies.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const template = warningTemplates[Math.floor(Math.random() * warningTemplates.length)];
    
    // 生成随机面积数据
    const areas = Array.from({length: 6}, () => (Math.random() * 200).toFixed(2));
    const recordAreas = areas.map(area => (parseFloat(area) * (0.8 + Math.random() * 0.4)).toFixed(2));
    
    let warningContent = template
      .replace('{code}', assetCode)
      .replace('{status}', status)
      .replace('{area1}', areas[0])
      .replace('{area2}', areas[1])
      .replace('{area3}', areas[2])
      .replace('{area4}', areas[3])
      .replace('{area5}', areas[4])
      .replace('{area6}', areas[5]);
      
    if (template.includes('{rarea1}')) {
      warningContent = warningContent
        .replace('{rarea1}', recordAreas[0])
        .replace('{rarea2}', recordAreas[1])
        .replace('{rarea3}', recordAreas[2])
        .replace('{rarea4}', recordAreas[3])
        .replace('{rarea5}', recordAreas[4])
        .replace('{rarea6}', recordAreas[5]);
    }
    
    additionalData.push({
      companyName: company,
      assetType: assetType,
      assetCode: assetCode,
      assetName: `${assetType}资产-${i.toString().padStart(4, '0')}`,
      warningContent: warningContent
    });
  }
  
  return [...mockAreaWarnData, ...additionalData];
};

const allMockData = generateMoreMockData();

export default [
  // 获取面积统计预警数据列表
  {
    url: `${baseUrl}/areaWarn/list`,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, companies } = query;
      
      let list = [...allMockData];
      
      // 企业筛选
      if (companies && companies.length > 0) {
        const companiesArray = Array.isArray(companies) ? companies : [companies];
        list = list.filter(item => companiesArray.includes(item.companyName));
      }
      
      // 分页处理
      const pageList = pagination(parseInt(page), parseInt(pageSize), list);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(page),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 导出面积统计预警数据
  {
    url: `${baseUrl}/areaWarn/export`,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出功能，实际应该返回文件流
      return resultSuccess('导出成功');
    },
  },
] as MockMethod[];
