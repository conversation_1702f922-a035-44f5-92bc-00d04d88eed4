import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 设备资产名称列表
const deviceNames = [
  '办公电脑设备',
  '打印机设备',
  '空调设备',
  '电梯设备',
  '监控设备',
  '网络设备',
  '音响设备',
  '投影设备',
  '复印机设备',
  '传真机设备',
  '扫描仪设备',
  '服务器设备',
  '存储设备',
  '交换机设备',
  '路由器设备',
  '电话设备',
  '照明设备',
  '消防设备',
  '安防设备',
  '门禁设备',
];

// 管理单位列表
const _manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
];

// 备注列表
const remarks = [
  '设备运行正常',
  '需要定期维护',
  '建议更新换代',
  '设备老化严重',
  '性能良好',
  '需要维修',
  '闲置状态',
  '出租使用中',
  '自用设备',
  '出借给其他部门',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成设备资产mock数据
const createDeviceData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const companyName = i % 7; // 0-6 对应不同的企业
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const normal = i % 2; // 0-可正常使用, 1-无法正常使用
    const freeOccupy = i % 2; // 0-是, 1-否

    // 生成资产使用状态（可能包含多个状态）
    const assetsStatusOptions = [
      [0], [1], [2], [3], [4], [5], [6], [7], [0, 1], [2, 5], [1, 2], [0, 3]
    ];
    const assetsStatus = assetsStatusOptions[i % assetsStatusOptions.length];

    // 根据设备类型生成不同的金额
    let deviceAmount = 0;
    let totalDepreciation = 0;
    let bookAmount = 0;
    let deviceDepreciationYears = 0;
    let remainingYearsOfEquipmentDepreciation = 0;

    switch (i % 5) {
      case 0: // 办公设备
        deviceAmount = Number((5000 + Math.random() * 15000).toFixed(2));
        deviceDepreciationYears = 5;
        break;
      case 1: // 电子设备
        deviceAmount = Number((8000 + Math.random() * 25000).toFixed(2));
        deviceDepreciationYears = 3;
        break;
      case 2: // 机械设备
        deviceAmount = Number((20000 + Math.random() * 80000).toFixed(2));
        deviceDepreciationYears = 10;
        break;
      case 3: // 运输设备
        deviceAmount = Number((50000 + Math.random() * 200000).toFixed(2));
        deviceDepreciationYears = 8;
        break;
      case 4: // 其他设备
        deviceAmount = Number((3000 + Math.random() * 12000).toFixed(2));
        deviceDepreciationYears = 6;
        break;
    }

    // 计算折旧
    const usedYears = Math.floor(Math.random() * deviceDepreciationYears);
    totalDepreciation = Number(((deviceAmount * usedYears) / deviceDepreciationYears).toFixed(2));
    bookAmount = Number((deviceAmount - totalDepreciation).toFixed(2));
    remainingYearsOfEquipmentDepreciation = deviceDepreciationYears - usedYears;

    // 生成资产编号
    const companyCode = '0016';
    const now = new Date();
    const yearMonth = now.getFullYear() + ('0' + (now.getMonth() + 1)).slice(-2);
    const serial = i.toString().padStart(5, '0');
    const assetCode = 'SB' + companyCode + yearMonth + serial;

    // 生成日期
    const createDate = generateRandomDate(new Date(2022, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());
    const entryDate = generateRandomDate(new Date(2020, 0, 1), createDate);
    const bookValueDate = generateRandomDate(entryDate, new Date());

    data.push({
      id: i,
      code: assetCode,
      enterpriseCode: 'E' + (20000 + i),
      name: deviceNames[i % deviceNames.length] + (i + 1),
      groupName: 0, // 集团固定为厦门市城市建设发展投资有限公司
      companyName: companyName,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      province: '福建省',
      city: '厦门市',
      area: ['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'][i % 6],
      address: '厦门市某某路' + (i + 1) + '号',
      status: status,
      assetsStatus: assetsStatus,
      normal: normal,
      freeOccupy: freeOccupy,
      assetEntryDate: formatDate(entryDate),
      deviceAmount: deviceAmount,
      totalDepreciation: totalDepreciation,
      deviceDepreciationYears: deviceDepreciationYears,
      remainingYearsOfEquipmentDepreciation: remainingYearsOfEquipmentDepreciation,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(bookValueDate),
      remark: Math.random() > 0.7 ? remarks[i % remarks.length] : '',
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: formatDate(createDate),
      updateTime: formatDate(updateDate),
    });
  }

  return data;
};

const deviceData = createDeviceData();

export default [
  {
    url: `${baseUrl}/device/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      const _start = (page - 1) * pageSize;
      const _end = _start + pageSize;
      let filteredData = [...deviceData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.includes(params.name));
      }
      if (params.code) {
        filteredData = filteredData.filter((item) => item.code.includes(params.code));
      }
      if (params.enterpriseCode) {
        filteredData = filteredData.filter((item) => item.enterpriseCode.includes(params.enterpriseCode));
      }
      if (params.companyName !== undefined && params.companyName !== '') {
        filteredData = filteredData.filter((item) => item.companyName === Number(params.companyName));
      }
      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter((item) => item.status === Number(params.status));
      }
      if (params.manageUnit !== undefined && params.manageUnit !== '') {
        filteredData = filteredData.filter((item) => item.manageUnit === Number(params.manageUnit));
      }
      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter((item) => item.reportOrNot === Number(params.reportOrNot));
      }
      if (params.normal !== undefined && params.normal !== '') {
        filteredData = filteredData.filter((item) => item.normal === Number(params.normal));
      }
      if (params.freeOccupy !== undefined && params.freeOccupy !== '') {
        filteredData = filteredData.filter((item) => item.freeOccupy === Number(params.freeOccupy));
      }
      if (params.operator) {
        filteredData = filteredData.filter((item) => item.operator.includes(params.operator));
      }
      if (params.entryClerk) {
        filteredData = filteredData.filter((item) => item.entryClerk.includes(params.entryClerk));
      }
      if (params.deviceAmountMin) {
        filteredData = filteredData.filter((item) => item.deviceAmount >= Number(params.deviceAmountMin));
      }
      if (params.deviceAmountMax) {
        filteredData = filteredData.filter((item) => item.deviceAmount <= Number(params.deviceAmountMax));
      }
      if (params.bookAmountMin) {
        filteredData = filteredData.filter((item) => item.bookAmount >= Number(params.bookAmountMin));
      }
      if (params.bookAmountMax) {
        filteredData = filteredData.filter((item) => item.bookAmount <= Number(params.bookAmountMax));
      }

      return resultPageSuccess(page, pageSize, filteredData);
    },
  },
  {
    url: `${baseUrl}/device/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = deviceData.length + 1;
      const newDevice = {
        id: newId,
        ...body,
        createTime: formatDate(new Date()),
        updateTime: formatDate(new Date()),
      };
      deviceData.push(newDevice);
      return resultSuccess(newDevice);
    },
  },
  {
    url: `${baseUrl}/device/edit`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const index = deviceData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        deviceData[index] = {
          ...deviceData[index],
          ...body,
          updateTime: formatDate(new Date()),
        };
        return resultSuccess(deviceData[index]);
      }
      return resultError('设备不存在');
    },
  },
  {
    url: `${baseUrl}/device/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const index = deviceData.findIndex((item) => item.id === Number(query.id));
      if (index !== -1) {
        deviceData.splice(index, 1);
        return resultSuccess('删除成功');
      }
      return resultError('设备不存在');
    },
  },
  {
    url: `${baseUrl}/device/deleteBatch`,
    timeout: 200,
    method: 'delete',
    response: ({ body }) => {
      const ids = body.ids || [];
      ids.forEach((id) => {
        const index = deviceData.findIndex((item) => item.id === id);
        if (index !== -1) {
          deviceData.splice(index, 1);
        }
      });
      return resultSuccess('批量删除成功');
    },
  },
  {
    url: `${baseUrl}/device/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const device = deviceData.find((item) => item.id === Number(query.id));
      if (device) {
        return resultSuccess(device);
      }
      return resultError('设备不存在');
    },
  },
  {
    url: `${baseUrl}/device/importExcel`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess('导入成功');
    },
  },
  {
    url: `${baseUrl}/device/exportXls`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('导出成功');
    },
  },
  {
    url: `${baseUrl}/device/exportAll`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('全部导出成功');
    },
  },
  {
    url: `${baseUrl}/device/downloadTemplate`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('模板下载成功');
    },
  },
] as MockMethod[];