import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 业务类型选项
const businessTypeOptions = [
  { label: '土地', value: 'land' },
  { label: '房屋', value: 'house' },
  { label: '广告位', value: 'advert' },
  { label: '设备', value: 'equipment' },
  { label: '其他', value: 'other' },
  { label: '租赁', value: 'rent' },
  { label: '租赁中止', value: 'rentAbort' },
  { label: '招租公告', value: 'rentAd' },
  { label: '租金明细', value: 'rentDetail' },
  { label: '转让', value: 'transfer' },
  { label: '空置/闲置', value: 'vacant' },
  { label: '占用', value: 'occupy' },
  { label: '借出', value: 'lend' },
  { label: '自用', value: 'self' },
  { label: '制度信息', value: 'systemInfo' },
  { label: '租金一览', value: 'rentOverview' },
  { label: '资产二维码', value: 'assetQRCode' },
];

// 模拟数据库
const mockDb = {
  downloadChecklistList: (() => {
    const result = [];
    for (let i = 0; i < 50; i++) {
      const hasResult = Random.boolean();
      const queryDate = new Date();
      // 在过去30天内生成一个随机时间点
      queryDate.setSeconds(queryDate.getSeconds() - Random.integer(0, 30 * 24 * 60 * 60));
      
      let businessType = null;
      if (Random.boolean()) { // 80%的概率有业务类型
        businessType = businessTypeOptions[Random.integer(0, businessTypeOptions.length - 1)].value;
      }

      result.push({
        id: i + 1,
        queryTime: queryDate.toISOString().replace('T', ' ').substring(0, 19),
        businessType: businessType,
        queryResult: hasResult ? 1 : 0,
        fileCount: hasResult ? Random.integer(1, 10) : 0,
        queryMethod: Random.boolean() ? 'auto' : 'manual',
      });
    }
    // 按查询时间倒序排序
    result.sort((a, b) => new Date(b.queryTime).getTime() - new Date(a.queryTime).getTime());
    return result;
  })(),
};

// 生成文件详情数据
function generateFileDetails(count: number) {
  const files = [];
  for (let i = 0; i < count; i++) {
    const fileDate = new Date();
    fileDate.setDate(fileDate.getDate() - Random.integer(0, 100));
    
    files.push({
      fileId: Random.string('lower', 32),
      fileName: `91110080M4017RH2R_5000_1800_${fileDate.toISOString().replace(/[-:T]/g, '').substring(0, 14)}.zip`,
      version: 1000 + i,
      fileSize: `${Random.float(0.1, 10, 1, 2)} MB`,
      businessType: businessTypeOptions[Random.integer(0, businessTypeOptions.length - 1)].value,
      fileTime: fileDate.toISOString().replace('T', ' ').substring(0, 19),
    });
  }
  return files;
}

export default [
  // 获取列表
  {
    url: `${baseUrl}/downloadChecklist/list`,
    method: 'get',
    response: ({ query }) => {
      const { pageNo = 1, pageSize = 10, businessTypes, queryTimeRange, queryResult, queryMethod } = query;
      
      let list = [...mockDb.downloadChecklistList];
      
      // 条件筛选
      if (businessTypes && businessTypes.length > 0) {
        const types = Array.isArray(businessTypes) ? businessTypes : [businessTypes];
        list = list.filter(item => item.businessType && types.includes(item.businessType));
      }
      
      if (queryTimeRange && queryTimeRange.length === 2) {
        const [startDate, endDate] = queryTimeRange;
        list = list.filter(item => {
          const itemDate = item.queryTime.substring(0, 10);
          return itemDate >= startDate && itemDate <= endDate;
        });
      }
      
      if (queryResult !== undefined && queryResult !== null && queryResult !== '') {
        list = list.filter(item => item.queryResult === parseInt(queryResult));
      }
      
      if (queryMethod) {
        list = list.filter(item => item.queryMethod === queryMethod);
      }
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 发起查询
  {
    url: `${baseUrl}/downloadChecklist/startQuery`,
    method: 'post',
    response: ({ body }) => {
      // 模拟查询过程
      setTimeout(() => {
        // 添加一条新的查询记录
        const newRecord = {
          id: mockDb.downloadChecklistList.length + 1,
          queryTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
          businessType: body.isSetType ? body.businessType : null,
          queryResult: Random.boolean() ? 1 : 0,
          fileCount: Random.boolean() ? Random.integer(1, 10) : 0,
          queryMethod: 'manual',
        };
        
        mockDb.downloadChecklistList.unshift(newRecord);
      }, 1000);
      
      return resultSuccess(true);
    },
  },
  
  // 获取文件详情
  {
    url: `${baseUrl}/downloadChecklist/fileDetails`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      
      // 查找对应ID的记录
      const item = mockDb.downloadChecklistList.find(item => item.id === parseInt(id));
      
      if (!item) {
        return resultError('未找到对应记录');
      }
      
      if (item.queryResult === 0) {
        return resultSuccess([]);
      }
      
      // 生成文件详情
      const fileDetails = generateFileDetails(item.fileCount);
      
      return resultSuccess(fileDetails);
    },
  },
] as MockMethod[];
