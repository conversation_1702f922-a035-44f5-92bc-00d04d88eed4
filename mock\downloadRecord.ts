import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 业务类型选项
const businessTypes = [
  'land', 'house', 'advert', 'equipment', 'other', 'rent', 'rentAbort', 
  'rentAd', 'rentDetail', 'transfer', 'vacant', 'occupy', 'lend', 
  'self', 'systemInfo', 'rentOverview', 'assetQRCode'
];

// 业务类型映射
const businessTypeMap = {
  'land': '土地',
  'house': '房屋',
  'advert': '广告位',
  'equipment': '设备',
  'other': '其他',
  'rent': '租赁',
  'rentAbort': '租赁中止',
  'rentAd': '招租公告',
  'rentDetail': '租金明细',
  'transfer': '转让',
  'vacant': '空置/闲置',
  'occupy': '占用',
  'lend': '借出',
  'self': '自用',
  'systemInfo': '制度信息',
  'rentOverview': '租金一览',
  'assetQRCode': '资产二维码'
};

// 生成32位随机文件ID
function generateFileId() {
  return [...Array(32)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
}

// 格式化日期时间
function formatDateTime(date, fmt = 'yyyy-MM-dd HH:mm:ss') {
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
    }
  }
  return fmt;
}

// 模拟数据库
const mockDb = {
  downloadRecordList: (() => {
    const result = [];
    for (let i = 0; i < 88; i++) {
      const downloadDate = new Date();
      // 在过去180天内生成一个随机时间点
      downloadDate.setSeconds(downloadDate.getSeconds() - Math.floor(Math.random() * 180 * 24 * 60 * 60));
      
      const businessType = businessTypes[Math.floor(Math.random() * businessTypes.length)];
      
      result.push({
        id: i + 1,
        fileId: generateFileId(),
        fileName: `91110080M4017RH2R_5000_1800_${formatDateTime(downloadDate, 'yyyyMMddHHmmss')}.zip`,
        version: 1000 + i,
        fileSize: `${(Math.random() * 50).toFixed(2)} MB`,
        businessType: businessType,
        businessTypeLabel: businessTypeMap[businessType],
        downloadTime: formatDateTime(downloadDate),
        createTime: formatDateTime(downloadDate),
      });
    }
    // 按下载时间倒序排序
    result.sort((a, b) => new Date(b.downloadTime).getTime() - new Date(a.downloadTime).getTime());
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/downloadRecord/list`,
    method: 'get',
    response: ({ query }) => {
      const { pageNo = 1, pageSize = 10, fileName, businessTypes, downloadTimeRange } = query;

      let list = [...mockDb.downloadRecordList];

      // 条件筛选
      if (fileName) {
        list = list.filter(item => item.fileName.includes(fileName));
      }

      if (businessTypes && businessTypes.length > 0) {
        const types = Array.isArray(businessTypes) ? businessTypes : [businessTypes];
        list = list.filter(item => types.includes(item.businessType));
      }

      if (downloadTimeRange && downloadTimeRange.length === 2) {
        const startTime = new Date(downloadTimeRange[0]).getTime();
        const endTime = new Date(downloadTimeRange[1]).getTime() + (24 * 60 * 60 * 1000 - 1); // 包含结束当天
        list = list.filter(item => {
          const itemTime = new Date(item.downloadTime).getTime();
          return itemTime >= startTime && itemTime <= endTime;
        });
      }

      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);

      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
] as MockMethod[];
