import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 资产名称列表
const assetNames = [
  '厦门市思明区商业广场',
  '湖里区工业厂房',
  '集美区办公楼',
  '海沧区仓储中心',
  '同安区生产基地',
  '翔安区物流园区',
  '福州市鼓楼区商业街',
  '台江区写字楼',
  '仓山区工业园',
  '泉州市鲤城区商业中心',
  '丰泽区办公大楼',
  '广州市天河区商业广场',
  '越秀区历史建筑',
  '深圳市南山区科技园',
  '福田区金融中心',
  '杭州市西湖区商业街',
  '宁波市海曙区办公楼',
];

// 空置资产名称列表
const idleAssetNames = [
  '思明区闲置商铺',
  '湖里区空置厂房',
  '集美区待租办公楼',
  '海沧区闲置仓库',
  '同安区空置生产基地',
  '翔安区待开发物流园',
  '鼓楼区空置商业街',
  '台江区闲置写字楼',
  '仓山区空置工业园',
  '鲤城区待租商业中心',
  '丰泽区空置办公大楼',
  '天河区闲置商业广场',
  '越秀区空置历史建筑',
  '南山区待租科技园',
  '福田区空置金融中心',
  '西湖区闲置商业街',
  '海曙区空置办公楼',
];

// 管理单位列表
const manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
  '福州市建设投资集团有限责任公司',
  '福州市城市建设投资有限公司',
  '泉州市建设发展集团有限公司',
  '广州市城市建设投资集团有限公司',
  '深圳市投资控股有限公司',
  '深圳市建设集团有限公司',
  '杭州市城市建设投资集团有限公司',
  '宁波市建设集团股份有限公司',
];

// 空置原因列表
const idleReasons = [
  '市场环境变化，需求下降',
  '设备老化，需要更新改造',
  '地理位置偏僻，交通不便',
  '租金过高，租户难以承受',
  '政策调整，用途受限',
  '维护成本高，经济效益差',
  '配套设施不完善',
  '安全隐患，需要整改',
  '规划调整，等待重新开发',
  '疫情影响，业务萎缩',
  '技术升级，原有设备淘汰',
  '环保要求，需要改造',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
];

// 备注列表
const remarks = [
  '需要重新评估市场价值',
  '建议进行功能改造',
  '可考虑出租给小微企业',
  '需要完善配套设施',
  '建议进行环境整治',
  '可考虑合作开发',
  '需要制定盘活方案',
  '建议进行资产重组',
  '可考虑政府回购',
  '需要专业团队运营',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 计算两个日期之间的天数
const calculateDays = (startDate: Date, endDate: Date) => {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// 生成空置信息mock数据
const createVacantData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 156; i++) {
    const assetType = i % 5; // 0-土地, 1-房屋, 2-设备, 3-广告位, 4-其他
    const status = i % 4 === 0 ? 4 : i % 3; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const reportOrNot = i % 3; // 0-否, 1-是, 2-未确定
    
    // 生成空置日期
    const startDate = generateRandomDate(new Date(2022, 0, 1), new Date(2024, 11, 31));
    const endDate = generateRandomDate(startDate, new Date(2025, 11, 31));
    const idleDays = calculateDays(startDate, endDate);
    
    // 根据资产类型生成不同的面积和金额
    let idleArea = 0;
    let assetsAmount = 0;
    let bookAmount = 0;
    
    switch (assetType) {
      case 0: // 土地
        idleArea = Number((1000 + Math.random() * 5000).toFixed(2));
        assetsAmount = Number((500 + Math.random() * 2000).toFixed(2));
        bookAmount = Number((400 + Math.random() * 1800).toFixed(2));
        break;
      case 1: // 房屋
        idleArea = Number((100 + Math.random() * 1000).toFixed(2));
        assetsAmount = Number((200 + Math.random() * 800).toFixed(2));
        bookAmount = Number((150 + Math.random() * 600).toFixed(2));
        break;
      case 2: // 设备
        idleArea = Number((10 + Math.random() * 100).toFixed(2));
        assetsAmount = Number((50 + Math.random() * 300).toFixed(2));
        bookAmount = Number((30 + Math.random() * 200).toFixed(2));
        break;
      case 3: // 广告位
        idleArea = Number((5 + Math.random() * 50).toFixed(2));
        assetsAmount = Number((20 + Math.random() * 100).toFixed(2));
        bookAmount = Number((15 + Math.random() * 80).toFixed(2));
        break;
      case 4: // 其他
        idleArea = Number((50 + Math.random() * 500).toFixed(2));
        assetsAmount = Number((100 + Math.random() * 400).toFixed(2));
        bookAmount = Number((80 + Math.random() * 300).toFixed(2));
        break;
    }

    data.push({
      id: i,
      code: `ZC${String(i).padStart(6, '0')}`,
      name: assetNames[i % assetNames.length],
      idleName: idleAssetNames[i % idleAssetNames.length],
      manageUnit: manageUnits[i % manageUnits.length],
      type: assetType,
      reportOrNot: reportOrNot,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      idleDays: idleDays,
      idleArea: idleArea,
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: generateRandomDate(new Date(2020, 0, 1), new Date(2024, 11, 31)).toISOString().split('T')[0],
      status: status,
      idleReason: idleReasons[i % idleReasons.length],
      remark: remarks[i % remarks.length],
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: generateRandomDate(new Date(2023, 0, 1), new Date())
        .toISOString(),
      updateTime: generateRandomDate(new Date(2023, 0, 1), new Date())
        .toISOString(),
      dealList: [] as any[], // 盘活记录列表
    });
  }

  return data;
};

// 生成闲置信息mock数据
const createIdleData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 93; i++) {
    const assetType = i % 5; // 0-土地, 1-房屋, 2-设备, 3-广告位, 4-其他
    const status = i % 4 === 0 ? 4 : i % 3; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const reportOrNot = i % 3; // 0-否, 1-是, 2-未确定
    
    // 生成闲置日期（闲置信息是180天以上的）
    const startDate = generateRandomDate(new Date(2022, 0, 1), new Date(2023, 6, 30));
    const endDate = generateRandomDate(startDate, new Date(2025, 11, 31));
    const idleDays = calculateDays(startDate, endDate);
    
    // 根据资产类型生成不同的面积和金额
    let idleArea = 0;
    let assetsAmount = 0;
    let bookAmount = 0;
    
    switch (assetType) {
      case 0: // 土地
        idleArea = Number((1000 + Math.random() * 5000).toFixed(2));
        assetsAmount = Number((500 + Math.random() * 2000).toFixed(2));
        bookAmount = Number((400 + Math.random() * 1800).toFixed(2));
        break;
      case 1: // 房屋
        idleArea = Number((100 + Math.random() * 1000).toFixed(2));
        assetsAmount = Number((200 + Math.random() * 800).toFixed(2));
        bookAmount = Number((150 + Math.random() * 600).toFixed(2));
        break;
      case 2: // 设备
        idleArea = Number((10 + Math.random() * 100).toFixed(2));
        assetsAmount = Number((50 + Math.random() * 300).toFixed(2));
        bookAmount = Number((30 + Math.random() * 200).toFixed(2));
        break;
      case 3: // 广告位
        idleArea = Number((5 + Math.random() * 50).toFixed(2));
        assetsAmount = Number((20 + Math.random() * 100).toFixed(2));
        bookAmount = Number((15 + Math.random() * 80).toFixed(2));
        break;
      case 4: // 其他
        idleArea = Number((50 + Math.random() * 500).toFixed(2));
        assetsAmount = Number((100 + Math.random() * 400).toFixed(2));
        bookAmount = Number((80 + Math.random() * 300).toFixed(2));
        break;
    }

    data.push({
      id: i,
      code: `XZXX${String(2023000 + i).padStart(7, '0')}`,
      name: assetNames[i % assetNames.length],
      idleName: `闲置资产-${i}`,
      manageUnit: manageUnits[i % manageUnits.length],
      type: assetType,
      reportOrNot: reportOrNot,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      idleDays: idleDays,
      idleArea: idleArea,
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: generateRandomDate(new Date(2020, 0, 1), new Date(2024, 11, 31)).toISOString().split('T')[0],
      status: status,
      idleReason: idleReasons[i % idleReasons.length],
      remark: remarks[i % remarks.length],
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: generateRandomDate(new Date(2023, 0, 1), new Date())
        .toISOString(),
      updateTime: generateRandomDate(new Date(2023, 0, 1), new Date())
        .toISOString(),
      dealList: [] as any[], // 盘活记录列表
    });
  }

  return data;
};

// 数据存储
const vacantData = createVacantData();
const idleData = createIdleData();

export default [
  // 查询空置信息列表
  {
    url: `${baseUrl}/vacant/list`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        name,
        code,
        type,
        status,
        manageUnit,
        reportOrNot,
        startDate,
        endDate,
        operator,
        entryClerk,
        column = 'createTime',
        order = 'desc',
      } = query;

      let filtered = [...vacantData];

      // 搜索过滤
      if (name) {
        filtered = filtered.filter((item) => item.name.includes(name));
      }

      if (code) {
        filtered = filtered.filter((item) => item.code.includes(code));
      }

      if (type !== undefined && type !== '') {
        filtered = filtered.filter((item) => item.type === parseInt(type));
      }

      if (status !== undefined && status !== '') {
        filtered = filtered.filter((item) => item.status === parseInt(status));
      }

      if (manageUnit) {
        filtered = filtered.filter((item) => item.manageUnit.includes(manageUnit));
      }

      if (reportOrNot !== undefined && reportOrNot !== '') {
        filtered = filtered.filter((item) => item.reportOrNot === parseInt(reportOrNot));
      }

      if (startDate) {
        filtered = filtered.filter((item) => item.startDate >= startDate);
      }

      if (endDate) {
        filtered = filtered.filter((item) => item.endDate <= endDate);
      }

      if (operator) {
        filtered = filtered.filter((item) => item.operator.includes(operator));
      }

      if (entryClerk) {
        filtered = filtered.filter((item) => item.entryClerk.includes(entryClerk));
      }

      // 排序
      filtered.sort((a, b) => {
        const aValue = a[column];
        const bValue = b[column];
        
        if (order === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // 分页处理
      const pageNum_ = parseInt(pageNum);
      const pageSize_ = parseInt(pageSize);

      return resultPageSuccess(pageNum_, pageSize_, filtered);
    },
  },

  // 获取空置信息详情
  {
    url: `${baseUrl}/vacant/detail`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const vacant = vacantData.find((item) => item.id === parseInt(id));
      if (vacant) {
        return resultSuccess(vacant);
      }
      return resultError('空置信息不存在');
    },
  },

  // 新增空置信息
  {
    url: `${baseUrl}/vacant/add`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const newId = Math.max(...vacantData.map(item => item.id)) + 1;
      const newVacant = {
        ...body,
        id: newId,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      };
      vacantData.push(newVacant);
      return resultSuccess(newVacant, { message: '新增成功' });
    },
  },

  // 编辑空置信息
  {
    url: `${baseUrl}/vacant/edit`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const index = vacantData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        vacantData[index] = {
          ...vacantData[index],
          ...body,
          updateTime: new Date().toISOString(),
        };
        return resultSuccess(vacantData[index], { message: '编辑成功' });
      }
      return resultError('空置信息不存在');
    },
  },

  // 删除空置信息
  {
    url: `${baseUrl}/vacant/delete`,
    timeout: 300,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = vacantData.findIndex((item) => item.id === parseInt(id));
      if (index !== -1) {
        vacantData.splice(index, 1);
        return resultSuccess(null, { message: '删除成功' });
      }
      return resultError('空置信息不存在');
    },
  },

  // 批量删除空置信息
  {
    url: `${baseUrl}/vacant/deleteBatch`,
    timeout: 300,
    method: 'delete',
    response: ({ body }) => {
      const { ids } = body;
      if (Array.isArray(ids)) {
        ids.forEach(id => {
          const index = vacantData.findIndex((item) => item.id === parseInt(id));
          if (index !== -1) {
            vacantData.splice(index, 1);
          }
        });
        return resultSuccess(null, { message: '批量删除成功' });
      }
      return resultError('参数错误');
    },
  },

  // 导入Excel
  {
    url: `${baseUrl}/vacant/importExcel`,
    timeout: 300,
    method: 'post',
    response: () => {
      return resultSuccess(null, { message: '导入成功' });
    },
  },

  // 导出Excel
  {
    url: `${baseUrl}/vacant/exportXls`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      let exportData = [];
      
      if (ids && Array.isArray(ids)) {
        exportData = vacantData.filter(item => ids.includes(item.id));
      } else {
        exportData = vacantData;
      }
      
      return resultSuccess(exportData, { message: '导出成功' });
    },
  },

  // 全部导出
  {
    url: `${baseUrl}/vacant/exportAll`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess(vacantData, { message: '全部导出成功' });
    },
  },

  // 获取资产列表（用于选择）
  {
    url: `${baseUrl}/asset/list`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { keyword } = query;
      let assetList = assetNames.map((name, index) => ({
        id: index + 1,
        name: name,
        code: `ZC${String(index + 1).padStart(6, '0')}`,
        type: index % 5,
      }));
      
      if (keyword) {
        assetList = assetList.filter(item => 
          item.name.includes(keyword) || item.code.includes(keyword)
        );
      }
      
      return resultSuccess(assetList);
    },
  },

  // 下载导入模板
  {
    url: `${baseUrl}/vacant/downloadTemplate`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess({
        url: '/api/vacant/template.xlsx',
        filename: '空置信息导入模板.xlsx'
      }, { message: '模板下载成功' });
    },
  },

  // ========== 闲置信息接口 ==========
  
  // 查询闲置信息列表
  {
    url: `${baseUrl}/idle/list`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        name,
        code,
        type,
        status,
        manageUnit,
        reportOrNot,
        startDate,
        endDate,
        operator,
        entryClerk,
        column = 'createTime',
        order = 'desc',
      } = query;

      let filtered = [...idleData];

      // 搜索过滤
      if (name) {
        filtered = filtered.filter((item) => item.name.includes(name));
      }

      if (code) {
        filtered = filtered.filter((item) => item.code.includes(code));
      }

      if (type !== undefined && type !== '') {
        filtered = filtered.filter((item) => item.type === parseInt(type));
      }

      if (status !== undefined && status !== '') {
        filtered = filtered.filter((item) => item.status === parseInt(status));
      }

      if (manageUnit) {
        filtered = filtered.filter((item) => item.manageUnit.includes(manageUnit));
      }

      if (reportOrNot !== undefined && reportOrNot !== '') {
        filtered = filtered.filter((item) => item.reportOrNot === parseInt(reportOrNot));
      }

      if (startDate) {
        filtered = filtered.filter((item) => item.startDate >= startDate);
      }

      if (endDate) {
        filtered = filtered.filter((item) => item.endDate <= endDate);
      }

      if (operator) {
        filtered = filtered.filter((item) => item.operator.includes(operator));
      }

      if (entryClerk) {
        filtered = filtered.filter((item) => item.entryClerk.includes(entryClerk));
      }

      // 排序
      filtered.sort((a, b) => {
        const aValue = a[column];
        const bValue = b[column];
        
        if (order === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // 分页处理
      const pageNum_ = parseInt(pageNum);
      const pageSize_ = parseInt(pageSize);

      return resultPageSuccess(pageNum_, pageSize_, filtered);
    },
  },

  // 获取闲置信息详情
  {
    url: `${baseUrl}/idle/detail`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const idle = idleData.find((item) => item.id === parseInt(id));
      if (idle) {
        return resultSuccess(idle);
      }
      return resultError('闲置信息不存在');
    },
  },

  // 新增闲置信息
  {
    url: `${baseUrl}/idle/add`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const newId = Math.max(...idleData.map(item => item.id)) + 1;
      const newIdle = {
        ...body,
        id: newId,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      };
      idleData.push(newIdle);
      return resultSuccess(newIdle, { message: '新增成功' });
    },
  },

  // 编辑闲置信息
  {
    url: `${baseUrl}/idle/edit`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const index = idleData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        idleData[index] = {
          ...idleData[index],
          ...body,
          updateTime: new Date().toISOString(),
        };
        return resultSuccess(idleData[index], { message: '编辑成功' });
      }
      return resultError('闲置信息不存在');
    },
  },

  // 删除闲置信息
  {
    url: `${baseUrl}/idle/delete`,
    timeout: 300,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = idleData.findIndex((item) => item.id === parseInt(id));
      if (index !== -1) {
        idleData.splice(index, 1);
        return resultSuccess(null, { message: '删除成功' });
      }
      return resultError('闲置信息不存在');
    },
  },

  // 批量删除闲置信息
  {
    url: `${baseUrl}/idle/deleteBatch`,
    timeout: 300,
    method: 'delete',
    response: ({ body }) => {
      const { ids } = body;
      if (Array.isArray(ids)) {
        ids.forEach(id => {
          const index = idleData.findIndex((item) => item.id === parseInt(id));
          if (index !== -1) {
            idleData.splice(index, 1);
          }
        });
        return resultSuccess(null, { message: '批量删除成功' });
      }
      return resultError('参数错误');
    },
  },

  // 导入Excel
  {
    url: `${baseUrl}/idle/importExcel`,
    timeout: 300,
    method: 'post',
    response: () => {
      return resultSuccess(null, { message: '导入成功' });
    },
  },

  // 导出Excel
  {
    url: `${baseUrl}/idle/exportXls`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      let exportData = [];
      
      if (ids && Array.isArray(ids)) {
        exportData = idleData.filter(item => ids.includes(item.id));
      } else {
        exportData = idleData;
      }
      
      return resultSuccess(exportData, { message: '导出成功' });
    },
  },

  // 全部导出
  {
    url: `${baseUrl}/idle/exportAll`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess(idleData, { message: '全部导出成功' });
    },
  },

  // 下载导入模板
  {
    url: `${baseUrl}/idle/downloadTemplate`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess({
        url: '/api/idle/template.xlsx',
        filename: '闲置信息导入模板.xlsx'
      }, { message: '模板下载成功' });
    },
  },
] as MockMethod[]; 