import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';

// 模拟空置闲置预警数据
const mockIdleWarnData = [
  // 土地 - 有产权
  {
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    totalArea: 45680.50,
    idleOver6MonthsArea: 5240.30,
    idleRateOver6Months: 11.47,
    idleUnder6MonthsArea: 2150.20,
    idleRateUnder6Months: 4.71,
    idle6To12MonthsArea: 2890.15,
    idle1To3YearsArea: 1850.10,
    idleOver3YearsArea: 500.05,
    idleAssetsCountOver6Months: 8,
    idleRateExcludeAgent: 11.47,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  // 土地 - 无产权
  {
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    totalArea: 28450.80,
    idleOver6MonthsArea: 3680.45,
    idleRateOver6Months: 12.94,
    idleUnder6MonthsArea: 1520.30,
    idleRateUnder6Months: 5.34,
    idle6To12MonthsArea: 2100.25,
    idle1To3YearsArea: 1280.15,
    idleOver3YearsArea: 300.05,
    idleAssetsCountOver6Months: 6,
    idleRateExcludeAgent: 12.94,
    companyName: '厦门市地热资源管理有限公司'
  },
  // 土地 - 代管
  {
    assetType: '土地',
    propertyRight: 'agent',
    propertyRightText: '代管',
    totalArea: 18920.40,
    idleOver6MonthsArea: 2450.60,
    idleRateOver6Months: 12.95,
    idleUnder6MonthsArea: 980.25,
    idleRateUnder6Months: 5.18,
    idle6To12MonthsArea: 1680.35,
    idle1To3YearsArea: 620.20,
    idleOver3YearsArea: 150.05,
    idleAssetsCountOver6Months: 4,
    idleRateExcludeAgent: 10.85,
    companyName: '厦门兴地房屋征迁服务有限公司'
  },
  // 房屋 - 有产权
  {
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    totalArea: 32580.75,
    idleOver6MonthsArea: 4890.40,
    idleRateOver6Months: 15.01,
    idleUnder6MonthsArea: 1850.30,
    idleRateUnder6Months: 5.68,
    idle6To12MonthsArea: 2680.25,
    idle1To3YearsArea: 1710.10,
    idleOver3YearsArea: 500.05,
    idleAssetsCountOver6Months: 12,
    idleRateExcludeAgent: 15.01,
    companyName: '厦门地丰置业有限公司'
  },
  // 房屋 - 无产权
  {
    assetType: '房屋',
    propertyRight: 'no',
    propertyRightText: '无产权',
    totalArea: 15680.60,
    idleOver6MonthsArea: 2340.85,
    idleRateOver6Months: 14.93,
    idleUnder6MonthsArea: 890.40,
    idleRateUnder6Months: 5.68,
    idle6To12MonthsArea: 1580.50,
    idle1To3YearsArea: 560.30,
    idleOver3YearsArea: 200.05,
    idleAssetsCountOver6Months: 7,
    idleRateExcludeAgent: 14.93,
    companyName: '图智策划咨询（厦门）有限公司'
  },
  // 房屋 - 代管
  {
    assetType: '房屋',
    propertyRight: 'agent',
    propertyRightText: '代管',
    totalArea: 12450.30,
    idleOver6MonthsArea: 1890.45,
    idleRateOver6Months: 15.18,
    idleUnder6MonthsArea: 680.20,
    idleRateUnder6Months: 5.46,
    idle6To12MonthsArea: 1280.25,
    idle1To3YearsArea: 460.15,
    idleOver3YearsArea: 150.05,
    idleAssetsCountOver6Months: 5,
    idleRateExcludeAgent: 12.85,
    companyName: '厦门市集众祥和物业管理有限公司'
  }
];

export default [
  // 获取空置闲置预警数据
  {
    url: `${baseUrl}/idleWarn/list`,
    method: 'get',
    response: ({ query }) => {
      const { assetType, propertyRight, isIdle, companies } = query;

      let list = [...mockIdleWarnData];

      // 资产类型筛选
      if (assetType) {
        const assetTypeMap = {
          'land': '土地',
          'house': '房屋',
        };
        
        if (assetTypeMap[assetType]) {
          list = list.filter(item => item.assetType === assetTypeMap[assetType]);
        }
      }
      
      // 产权状况筛选
      if (propertyRight) {
        list = list.filter(item => item.propertyRight === propertyRight);
      }
      
      // 是否闲置筛选
      if (isIdle === 'yes') {
        list = list.filter(item => item.idleOver6MonthsArea > 0);
      } else if (isIdle === 'no') {
        list = list.filter(item => item.idleOver6MonthsArea === 0);
      }
      
      // 所属企业筛选
      if (companies && companies.length > 0) {
        const companiesArray = Array.isArray(companies) ? companies : [companies];
        list = list.filter(item => item.companyName && companiesArray.includes(item.companyName));
      }
      
      return resultSuccess(list);
    },
  },
  
  // 导出空置闲置预警数据
  {
    url: `${baseUrl}/idleWarn/export`,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出功能，实际应该返回文件流
      return resultSuccess('导出成功');
    },
  },
] as MockMethod[];
