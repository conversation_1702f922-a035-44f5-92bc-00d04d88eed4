import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 土地资产名称列表
const landNames = [
  '厦门市思明区商业广场用地',
  '湖里区工业厂房用地',
  '集美区住宅小区用地',
  '海沧区仓储物流用地',
  '同安区农业示范基地',
  '翔安区物流园区用地',
  '福州市鼓楼区商业街用地',
  '台江区写字楼用地',
  '仓山区工业园区用地',
  '泉州市鲤城区商业中心用地',
  '丰泽区办公大楼用地',
  '广州市天河区商业广场用地',
  '越秀区历史文化用地',
  '深圳市南山区科技园用地',
  '福田区金融中心用地',
  '杭州市西湖区商业街用地',
  '宁波市海曙区办公楼用地',
  '厦门市集美区大学城用地',
  '厦门市海沧区港口用地',
  '厦门市同安区工业园用地',
];

// 管理单位列表
const _manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
  '福州市建设投资集团有限责任公司',
  '福州市城市建设投资有限公司',
  '泉州市建设发展集团有限公司',
  '广州市城市建设投资集团有限公司',
  '深圳市投资控股有限公司',
  '深圳市建设集团有限公司',
  '杭州市城市建设投资集团有限公司',
  '宁波市建设集团股份有限公司',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
];

// 备注列表
const _remarks = [
  '土地规划调整中',
  '等待开发审批',
  '需要环境评估',
  '建议进行土地整理',
  '可考虑合作开发',
  '需要完善配套设施',
  '建议进行资产重组',
  '可考虑政府回购',
  '需要专业团队运营',
  '等待市场时机',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 生成土地资产mock数据
const createLandData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const useType = i % 4; // 0-居住, 1-商业, 2-工业, 3-其他
    const construction = i % 4; // 0-空地, 1-部分在建, 2-分期开发, 3-已完成建设
    const gainType = i % 9; // 0-划拨, 1-招拍挂出让, 2-自建, 3-购买, 4-赠送, 5-股权转让, 6-借用, 7-租赁, 8-其他
    const propertyType = i % 3; // 0-否, 1-是, 2-代管
    const companyName = i % 7; // 0-6 对应不同的企业
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const offAccount = i % 2; // 0-否, 1-是
    const insuranceOrNot = i % 2; // 0-否, 1-是
    const mortgageOrNot = i % 2; // 0-否, 1-是
    const completionOrNot = i % 2; // 0-否, 1-是
    const owingOrNot = i % 2; // 0-否, 1-是
    const vitalizeOrNot = i % 2; // 0-否, 1-是
    const warrantIntegration = i % 2; // 0-否, 1-是

    // 生成资产使用状态（可能包含多个状态）
    const assetsStatusOptions = [
      ['闲置'], ['自用'], ['出租'], ['出借'], ['占用'], ['欠租'], ['转让'], ['其他'],
      ['闲置', '自用'], ['出租', '欠租'], ['自用', '出租'], ['闲置', '出借']
    ];
    const assetsStatus = assetsStatusOptions[i % assetsStatusOptions.length];

    // 根据土地类型生成不同的面积和金额
    let landArea = 0;
    let rentableArea = 0;
    let propertyArea = 0;
    let notPropertyArea = 0;
    let assetsAmount = 0;
    let bookAmount = 0;
    let landPrice = 0;
    let landArrears = 0;

    switch (useType) {
      case 0: // 居住
        landArea = Number((5000 + Math.random() * 20000).toFixed(2));
        rentableArea = Number((landArea * 0.8).toFixed(2));
        propertyArea = Number((landArea * 0.9).toFixed(2));
        notPropertyArea = Number((landArea * 0.1).toFixed(2));
        assetsAmount = Number((2000 + Math.random() * 8000).toFixed(2));
        bookAmount = Number((1500 + Math.random() * 6000).toFixed(2));
        landPrice = Number((1500 + Math.random() * 5000).toFixed(2));
        landArrears = Number((Math.random() * 500).toFixed(2));
        break;
      case 1: // 商业
        landArea = Number((3000 + Math.random() * 15000).toFixed(2));
        rentableArea = Number((landArea * 0.85).toFixed(2));
        propertyArea = Number((landArea * 0.95).toFixed(2));
        notPropertyArea = Number((landArea * 0.05).toFixed(2));
        assetsAmount = Number((3000 + Math.random() * 12000).toFixed(2));
        bookAmount = Number((2500 + Math.random() * 10000).toFixed(2));
        landPrice = Number((2500 + Math.random() * 8000).toFixed(2));
        landArrears = Number((Math.random() * 800).toFixed(2));
        break;
      case 2: // 工业
        landArea = Number((8000 + Math.random() * 30000).toFixed(2));
        rentableArea = Number((landArea * 0.7).toFixed(2));
        propertyArea = Number((landArea * 0.8).toFixed(2));
        notPropertyArea = Number((landArea * 0.2).toFixed(2));
        assetsAmount = Number((1500 + Math.random() * 6000).toFixed(2));
        bookAmount = Number((1200 + Math.random() * 5000).toFixed(2));
        landPrice = Number((1000 + Math.random() * 4000).toFixed(2));
        landArrears = Number((Math.random() * 300).toFixed(2));
        break;
      case 3: // 其他
        landArea = Number((4000 + Math.random() * 18000).toFixed(2));
        rentableArea = Number((landArea * 0.75).toFixed(2));
        propertyArea = Number((landArea * 0.85).toFixed(2));
        notPropertyArea = Number((landArea * 0.15).toFixed(2));
        assetsAmount = Number((1000 + Math.random() * 4000).toFixed(2));
        bookAmount = Number((800 + Math.random() * 3200).toFixed(2));
        landPrice = Number((800 + Math.random() * 3000).toFixed(2));
        landArrears = Number((Math.random() * 200).toFixed(2));
        break;
    }

    // 根据使用状态生成对应的面积数据
    let idleArea = 0;
    let useArea = 0;
    let rentArea = 0;
    let lendArea = 0;
    let occupyArea = 0;
    let sellArea = 0;
    let otherArea = 0;

    if (assetsStatus.includes('闲置')) {
      idleArea = Number((landArea * 0.3).toFixed(2));
    }
    if (assetsStatus.includes('自用')) {
      useArea = Number((landArea * 0.4).toFixed(2));
    }
    if (assetsStatus.includes('出租')) {
      rentArea = Number((landArea * 0.5).toFixed(2));
    }
    if (assetsStatus.includes('出借')) {
      lendArea = Number((landArea * 0.2).toFixed(2));
    }
    if (assetsStatus.includes('占用')) {
      occupyArea = Number((landArea * 0.25).toFixed(2));
    }
    if (assetsStatus.includes('转让')) {
      sellArea = Number((landArea * 0.1).toFixed(2));
    }
    if (assetsStatus.includes('其他')) {
      otherArea = Number((landArea * 0.15).toFixed(2));
    }

    // 生成盘活记录
    const dealCount = Math.floor(Math.random() * 4);
    const dealList: any[] = [];
    for (let j = 0; j < dealCount; j++) {
      dealList.push({
        date: generateRandomDate(new Date(2023, 0, 1), new Date())
          .toISOString()
          .split('T')[0],
        isResult: Math.floor(Math.random() * 2),
        vitalizeType: Math.floor(Math.random() * 7),
        reason: `盘活原因-${j + 1}`,
        nextReason: `后续计划-${j + 1}`,
      });
    }

    data.push({
      id: i,
      code: `TD0016${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(i).padStart(5, '0')}`,
      enterpriseCode: `E${Math.floor(Math.random() * 10000)}`,
      name: landNames[i % landNames.length],
      groupName: 0, // 固定为厦门市城市建设发展投资有限公司
      companyName: companyName,
      ownUnit: `所有权单位${i}`,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: generateRandomDate(new Date(2020, 0, 1), new Date()).toISOString(),
      updateTime: generateRandomDate(new Date(2020, 0, 1), new Date()).toISOString(),
      location: `福建省厦门市${['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'][i % 6]}`,
      address: `厦门市${['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'][i % 6]}XX街道${i}号`,
      status: status,
      useType: useType,
      useTypeInput: useType === 3 ? '特定用途说明' : '',
      construction: construction,
      gainDate: generateRandomDate(new Date(2010, 0, 1), new Date(2024, 11, 31)).toISOString().split('T')[0],
      gainType: gainType,
      landPrice: landPrice,
      landArrears: landArrears,
      landArea: landArea,
      rentableArea: rentableArea,
      propertyArea: propertyArea,
      notPropertyArea: notPropertyArea,
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: generateRandomDate(new Date(2020, 0, 1), new Date(2024, 11, 31)).toISOString().split('T')[0],
      propertyType: propertyType,
      warrantDate: generateRandomDate(new Date(2015, 0, 1), new Date(2024, 11, 31)).toISOString().split('T')[0],
      custodyEntrustingParty: propertyType === 2 ? `委托方${i}` : '',
      warrantIntegration: warrantIntegration,
      landWarrant: `土地权证${i}号`,
      offAccount: offAccount,
      insuranceOrNot: insuranceOrNot,
      mortgageOrNot: mortgageOrNot,
      completionOrNot: completionOrNot,
      owingOrNot: owingOrNot,
      vitalizeOrNot: vitalizeOrNot,
      workProgress: '工作进展顺利。',
      problems: '暂无问题。',
      assetsStatus: assetsStatus,
      idleArea: idleArea,
      useArea: useArea,
      rentArea: rentArea,
      lendArea: lendArea,
      occupyArea: occupyArea,
      sellArea: sellArea,
      otherArea: otherArea,
      remark: Math.random() > 0.7 ? `备注信息${i}` : '',
      dealList: dealList,
    });
  }

  return data;
};

// 数据存储
const landData = createLandData();

export default [
  // 获取土地资产列表
  {
    url: `${baseUrl}/land/list`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { pageNum = 1, pageSize = 10, name, code, status, companyName, manageUnit, useType, construction, gainType, propertyType, assetsStatus, offAccount, insuranceOrNot, mortgageOrNot, minArea, maxArea, operator, createTime, updateTime, reportOrNot, completionOrNot, owingOrNot, vitalizeOrNot, region, column = 'createTime', order = 'desc' } = query;

      let filtered = [...landData];

      // 搜索过滤
      if (name) {
        filtered = filtered.filter((item) => item.name.includes(name));
      }

      if (code) {
        filtered = filtered.filter((item) => item.code.includes(code));
      }

      if (status !== undefined && status !== '') {
        filtered = filtered.filter((item) => item.status === parseInt(status));
      }

      if (companyName !== undefined && companyName !== '') {
        filtered = filtered.filter((item) => item.companyName === parseInt(companyName));
      }

      if (manageUnit !== undefined && manageUnit !== '') {
        filtered = filtered.filter((item) => item.manageUnit === parseInt(manageUnit));
      }

            // 省市区级联选择过滤
      if (region && Array.isArray(region) && region.length > 0) {
        const [provinceCode, cityCode, districtCode] = region;
        
        // 地区代码到文本的映射
        const areaCodeMap = {
          // 福建省
          '350000': '福建省',
          '350200': '厦门市',
          '350203': '思明区',
          '350206': '湖里区',
          '350211': '集美区',
          '350205': '海沧区',
          '350212': '同安区',
          '350213': '翔安区',
          '350100': '福州市',
          '350102': '鼓楼区',
          '350103': '台江区',
          '350104': '仓山区',
          '350105': '马尾区',
          '350111': '晋安区',
          '350500': '泉州市',
          '350502': '鲤城区',
          '350503': '丰泽区',
          '350504': '洛江区',
          '350505': '泉港区',
          
          // 广东省
          '440000': '广东省',
          '440100': '广州市',
          '440106': '天河区',
          '440104': '越秀区',
          '440103': '荔湾区',
          '440105': '海珠区',
          '440111': '白云区',
          '440300': '深圳市',
          '440305': '南山区',
          '440304': '福田区',
          '440303': '罗湖区',
          '440306': '宝安区',
          '440307': '龙岗区',
          '441900': '东莞市',
          '441902': '莞城区',
          '441903': '南城区',
          '441904': '东城区',
          '441905': '万江区',
          
          // 浙江省
          '330000': '浙江省',
          '330100': '杭州市',
          '330106': '西湖区',
          '330102': '上城区',
          '330105': '拱墅区',
          '330110': '余杭区',
          '330200': '宁波市',
          '330203': '海曙区',
          '330205': '江北区',
          '330212': '鄞州区',
          '330211': '镇海区',
        };
        
        filtered = filtered.filter((item) => {
          const location = item.location;
          if (provinceCode && areaCodeMap[provinceCode] && !location.includes(areaCodeMap[provinceCode])) return false;
          if (cityCode && areaCodeMap[cityCode] && !location.includes(areaCodeMap[cityCode])) return false;
          if (districtCode && areaCodeMap[districtCode] && !location.includes(areaCodeMap[districtCode])) return false;
          return true;
        });
      }

      if (useType !== undefined && useType !== '') {
        filtered = filtered.filter((item) => item.useType === parseInt(useType));
      }

      if (construction !== undefined && construction !== '' && Array.isArray(construction)) {
        filtered = filtered.filter((item) => construction.includes(item.construction));
      } else if (construction !== undefined && construction !== '') {
        filtered = filtered.filter((item) => item.construction === parseInt(construction));
      }

      if (gainType !== undefined && gainType !== '') {
        filtered = filtered.filter((item) => item.gainType === parseInt(gainType));
      }

      if (propertyType !== undefined && propertyType !== '') {
        filtered = filtered.filter((item) => item.propertyType === parseInt(propertyType));
      }

      if (offAccount !== undefined && offAccount !== '') {
        filtered = filtered.filter((item) => item.offAccount === parseInt(offAccount));
      }

      if (insuranceOrNot !== undefined && insuranceOrNot !== '') {
        filtered = filtered.filter((item) => item.insuranceOrNot === parseInt(insuranceOrNot));
      }

      if (mortgageOrNot !== undefined && mortgageOrNot !== '') {
        filtered = filtered.filter((item) => item.mortgageOrNot === parseInt(mortgageOrNot));
      }

      if (minArea !== undefined && minArea !== '') {
        filtered = filtered.filter((item) => item.landArea >= parseFloat(minArea));
      }

      if (maxArea !== undefined && maxArea !== '') {
        filtered = filtered.filter((item) => item.landArea <= parseFloat(maxArea));
      }

      if (operator) {
        filtered = filtered.filter((item) => item.operator.includes(operator));
      }

      if (createTime && createTime.length === 2) {
        const [startDate, endDate] = createTime;
        filtered = filtered.filter((item) => {
          const itemDate = new Date(item.createTime);
          return itemDate >= new Date(startDate) && itemDate <= new Date(endDate);
        });
      }

      if (updateTime && updateTime.length === 2) {
        const [startDate, endDate] = updateTime;
        filtered = filtered.filter((item) => {
          const itemDate = new Date(item.updateTime);
          return itemDate >= new Date(startDate) && itemDate <= new Date(endDate);
        });
      }

      if (reportOrNot !== undefined && reportOrNot !== '') {
        filtered = filtered.filter((item) => item.reportOrNot === parseInt(reportOrNot));
      }

      if (completionOrNot !== undefined && completionOrNot !== '') {
        filtered = filtered.filter((item) => item.completionOrNot === parseInt(completionOrNot));
      }

      if (owingOrNot !== undefined && owingOrNot !== '') {
        filtered = filtered.filter((item) => item.owingOrNot === parseInt(owingOrNot));
      }

      if (vitalizeOrNot !== undefined && vitalizeOrNot !== '') {
        filtered = filtered.filter((item) => item.vitalizeOrNot === parseInt(vitalizeOrNot));
      }

      // 排序
      filtered.sort((a, b) => {
        const aValue = a[column];
        const bValue = b[column];

        if (order === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });

      // 分页处理
      const pageNum_ = parseInt(pageNum);
      const pageSize_ = parseInt(pageSize);

      return resultPageSuccess(pageNum_, pageSize_, filtered);
    },
  },

  // 新增土地资产
  {
    url: `${baseUrl}/land/add`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const newId = Math.max(...landData.map((item) => item.id)) + 1;
      const newLand = {
        ...body,
        id: newId,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      };

      landData.unshift(newLand);

      return resultSuccess(newLand, { message: '新增成功' });
    },
  },

  // 编辑土地资产
  {
    url: `${baseUrl}/land/edit`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const index = landData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        landData[index] = {
          ...landData[index],
          ...body,
          updateTime: new Date().toISOString(),
        };

        return resultSuccess(landData[index], { message: '更新成功' });
      }

      return resultError('数据不存在');
    },
  },

  // 删除土地资产
  {
    url: `${baseUrl}/land/delete`,
    timeout: 300,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = landData.findIndex((item) => item.id === parseInt(id));

      if (index !== -1) {
        landData.splice(index, 1);
        return resultSuccess(null, { message: '删除成功' });
      }

      return resultError('数据不存在');
    },
  },

  // 批量删除土地资产
  {
    url: `${baseUrl}/land/deleteBatch`,
    timeout: 300,
    method: 'delete',
    response: ({ body }) => {
      const { ids } = body;
      const deletedCount = ids.length;

      ids.forEach((id) => {
        const index = landData.findIndex((item) => item.id === id);
        if (index !== -1) {
          landData.splice(index, 1);
        }
      });

      return resultSuccess(null, { message: `成功删除${deletedCount}条数据` });
    },
  },

  // 获取土地资产详情
  {
    url: `${baseUrl}/land/detail`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const land = landData.find((item) => item.id === parseInt(id));

      if (land) {
        return resultSuccess(land);
      }

      return resultError('数据不存在');
    },
  },

  // 导入Excel
  {
    url: `${baseUrl}/land/importExcel`,
    timeout: 300,
    method: 'post',
    response: () => {
      return resultSuccess(
        {
          successCount: 10,
          failCount: 0,
          failList: [],
        },
        { message: '导入成功' }
      );
    },
  },

  // 导出Excel
  {
    url: `${baseUrl}/land/exportXls`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      let exportData: any[] = [];

      if (ids && Array.isArray(ids)) {
        exportData = landData.filter((item) => ids.includes(item.id));
      } else {
        exportData = landData;
      }

      return resultSuccess(exportData, { message: '导出成功' });
    },
  },

  // 全部导出
  {
    url: `${baseUrl}/land/exportAll`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess(landData, { message: '导出成功' });
    },
  },

  // 下载模板
  {
    url: `${baseUrl}/land/downloadTemplate`,
    timeout: 300,
    method: 'get',
    response: () => {
      return resultSuccess(
        {
          url: '/api/land/template.xlsx',
          filename: '土地资产导入模板.xlsx',
        },
        { message: '下载成功' }
      );
    },
  },
] as MockMethod[];
