import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 资产名称列表
const assetNames = [
  '厦门市思明区某地块',
  '厦门市湖里区某办公楼',
  '厦门市集美区某厂房',
  '厦门市海沧区某仓库',
  '厦门市同安区某商业楼',
  '厦门市翔安区某住宅楼',
  '厦门市思明区某商铺',
  '厦门市湖里区某停车场',
  '厦门市集美区某广告牌',
  '厦门市海沧区某设备',
];

// 被占用资产名称列表
const occupyNames = [
  '临时停车场',
  '临时仓库',
  '临时办公场所',
  '临时施工场地',
  '临时堆放场地',
  '临时广告位',
  '临时设备存放',
  '临时人员住宿',
  '临时会议场所',
  '临时展示场地',
];

// 管理单位列表
const manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = ['张建国', '李明华', '王志强', '陈文博', '刘德华', '赵敏', '孙志远', '周晓明', '吴建设', '郑海峰'];

// 借用人列表
const lendUsers = [
  '市政建设公司',
  '房地产开发公司',
  '广告传媒公司',
  '物流运输公司',
  '工程施工队',
  '临时租户',
  '政府部门',
  '其他企业',
  '教育机构',
  '医疗机构',
];

// 借用原因列表
const lendReasons = [
  '因市政规划需要临时借用',
  '因工程施工需要临时借用',
  '因广告宣传需要临时借用',
  '因仓储物流需要临时借用',
  '因办公需要临时借用',
  '因展示需要临时借用',
  '因教学需要临时借用',
  '因医疗需要临时借用',
  '因其他业务需要临时借用',
];

// 备注列表
const remarks = [
  '借用期间需保持场地整洁',
  '借用期满后需归还',
  '借用期间需注意安全',
  '借用期间需遵守相关规定',
  '借用期间需按时缴纳费用',
  '借用期间需配合管理',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成借出信息mock数据
const createLendData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const type = i % 5; // 0-土地, 1-房屋, 2-设备, 3-广告位, 4-其他
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const changeUse = i % 2; // 0-否, 1-是

    // 生成起始日期和结束日期（确保结束日期晚于起始日期）
    const startDate = generateRandomDate(new Date(2023, 0, 1), new Date());
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 365) + 30); // 至少30天

    // 计算借出天数
    const lendDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // 随机资产价值（原值）
    const assetsAmount = Number((Math.random() * 1000 + 100).toFixed(2));

    // 随机账面价值（小于等于原值）
    const bookAmount = Number((Math.random() * assetsAmount).toFixed(2));

    // 随机借出面积
    const lendArea = Number((Math.random() * 1000 + 10).toFixed(2));

    // 创建时间和更新时间
    const createDate = generateRandomDate(new Date(2022, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());

    const dateOfBookValue = generateRandomDate(createDate, new Date());

    // 经办人和录入人
    const operator = operators[i % operators.length];
    const entryClerk = operators[i % operators.length];

    // 生成资产编号
    const companyCode = '0016';
    const now = new Date();
    const yearMonth = now.getFullYear() + ('0' + (now.getMonth() + 1)).slice(-2);
    const serial = i.toString().padStart(5, '0');
    const assetCode = 'JC' + companyCode + yearMonth + serial;

    data.push({
      id: i,
      name: assetNames[i % assetNames.length] + (i + 1),
      occupyName: Math.random() > 0.5 ? occupyNames[i % occupyNames.length] + (i + 1) : '',
      code: assetCode,
      type: type,
      manageUnit: manageUnits[manageUnit],
      reportOrNot: reportOrNot,
      lendUser: lendUsers[i % lendUsers.length],
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      lendDays: lendDays,
      lendArea: lendArea,
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(dateOfBookValue),
      changeUse: changeUse,
      lendReason: lendReasons[i % lendReasons.length],
      status: status,
      remark: Math.random() > 0.7 ? remarks[i % remarks.length] : '',
      operator: operator,
      entryClerk: entryClerk,
      createTime: formatDate(createDate),
      updateTime: formatDate(updateDate),
    });
  }

  return data;
};

const lendData = createLendData();

export default [
  {
    url: `${baseUrl}/lend/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      const _start = (page - 1) * pageSize;
      const _end = _start + pageSize;
      let filteredData = [...lendData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.includes(params.name));
      }
      if (params.code) {
        filteredData = filteredData.filter((item) => item.code.includes(params.code));
      }
      if (params.occupyName) {
        filteredData = filteredData.filter((item) => item.occupyName && item.occupyName.includes(params.occupyName));
      }
      if (params.lendUser) {
        filteredData = filteredData.filter((item) => item.lendUser.includes(params.lendUser));
      }
      if (params.type !== undefined && params.type !== '') {
        filteredData = filteredData.filter((item) => item.type === Number(params.type));
      }
      if (params.manageUnit !== undefined && params.manageUnit !== '') {
        filteredData = filteredData.filter((item) => item.manageUnit === manageUnits[Number(params.manageUnit)]);
      }
      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter((item) => item.reportOrNot === Number(params.reportOrNot));
      }
      if (params.changeUse !== undefined && params.changeUse !== '') {
        filteredData = filteredData.filter((item) => item.changeUse === Number(params.changeUse));
      }
      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter((item) => item.status === Number(params.status));
      }
      if (params.operator) {
        filteredData = filteredData.filter((item) => item.operator.includes(params.operator));
      }
      if (params.entryClerk) {
        filteredData = filteredData.filter((item) => item.entryClerk.includes(params.entryClerk));
      }

      // 日期范围过滤
      if (params.startDateBegin && params.startDateEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.startDate);
          const beginDate = new Date(params.startDateBegin);
          const endDate = new Date(params.startDateEnd);
          return itemDate >= beginDate && itemDate <= endDate;
        });
      }
      if (params.endDateBegin && params.endDateEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.endDate);
          const beginDate = new Date(params.endDateBegin);
          const endDate = new Date(params.endDateEnd);
          return itemDate >= beginDate && itemDate <= endDate;
        });
      }

      // 数值范围过滤
      if (params.minLendDays !== undefined) {
        filteredData = filteredData.filter((item) => item.lendDays >= Number(params.minLendDays));
      }
      if (params.maxLendDays !== undefined) {
        filteredData = filteredData.filter((item) => item.lendDays <= Number(params.maxLendDays));
      }
      if (params.minLendArea !== undefined) {
        filteredData = filteredData.filter((item) => item.lendArea >= Number(params.minLendArea));
      }
      if (params.maxLendArea !== undefined) {
        filteredData = filteredData.filter((item) => item.lendArea <= Number(params.maxLendArea));
      }
      if (params.minBookAmount !== undefined) {
        filteredData = filteredData.filter((item) => item.bookAmount >= Number(params.minBookAmount));
      }
      if (params.maxBookAmount !== undefined) {
        filteredData = filteredData.filter((item) => item.bookAmount <= Number(params.maxBookAmount));
      }

      // 创建时间范围过滤
      if (params.createTimeBegin && params.createTimeEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.createTime);
          const beginDate = new Date(params.createTimeBegin);
          const endDate = new Date(params.createTimeEnd);
          return itemDate >= beginDate && itemDate <= endDate;
        });
      }

      // 更新时间范围过滤
      if (params.updateTimeBegin && params.updateTimeEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.updateTime);
          const beginDate = new Date(params.updateTimeBegin);
          const endDate = new Date(params.updateTimeEnd);
          return itemDate >= beginDate && itemDate <= endDate;
        });
      }

      const result = filteredData.slice(_start, _end);
      return resultPageSuccess(page, pageSize, result);
    },
  },
  {
    url: `${baseUrl}/lend/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = lendData.length + 1;
      const newData = {
        ...body,
        id: newId,
        createTime: formatDate(new Date()),
        updateTime: formatDate(new Date()),
      };
      lendData.push(newData);
      return resultSuccess(newData);
    },
  },
  {
    url: `${baseUrl}/lend/edit`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const index = lendData.findIndex(item => item.id === body.id);
      if (index !== -1) {
        lendData[index] = {
          ...lendData[index],
          ...body,
          updateTime: formatDate(new Date()),
        };
        return resultSuccess(lendData[index]);
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/lend/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = lendData.findIndex(item => item.id === Number(id));
      if (index !== -1) {
        lendData.splice(index, 1);
        return resultSuccess('删除成功');
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/lend/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      console.log('query', query);
      const id = query.id;
      const data = lendData.find((item) => item.id === Number(id));
      if (data) {
        return resultSuccess(data);
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/lend/export`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      if (ids) {
        const idArray = ids.split(',').map(Number);
        const exportData = lendData.filter(item => idArray.includes(item.id));
        return resultSuccess(`成功导出${exportData.length}条数据`);
      }
      return resultError('请选择要导出的数据');
    },
  },
  {
    url: `${baseUrl}/lend/exportAll`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess(`成功导出全部${lendData.length}条数据`);
    },
  },
  {
    url: `${baseUrl}/lend/import`,
    timeout: 200,
    method: 'post',
    response: () => {
      // 模拟导入成功
      return resultSuccess('成功导入20条数据');
    },
  },
  {
    url: `${baseUrl}/lend/template`,
    timeout: 200,
    method: 'get',
    response: () => {
      // 模拟下载模板
      return resultSuccess('模板下载成功');
    },
  },
] as MockMethod[]; 