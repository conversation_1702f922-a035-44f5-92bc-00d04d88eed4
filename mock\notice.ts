import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 公告标题列表
const announcementTitles = [
  '厦门市思明区商业资产招租公告',
  '厦门市湖里区商业资产招租公告',
  '厦门市集美区商业资产招租公告',
  '厦门市海沧区商业资产招租公告',
  '厦门市同安区商业资产招租公告',
  '厦门市翔安区商业资产招租公告',
  '厦门市思明区写字楼招租公告',
  '厦门市湖里区写字楼招租公告',
  '厦门市集美区写字楼招租公告',
  '厦门市海沧区写字楼招租公告',
];

// 所属企业列表
const enterpriseOptions = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = ['张三', '李四', '王五', '赵六', '陈七', '林八', '吴九', '郑十'];

// 招租方式列表
const _rentTypeMap = {
  0: '公开招租（进场）',
  1: '异地公开招租（进场）',
  2: '公开招租（非进场）',
  5: '其他方式招租（专业化招商）',
};

// 状态映射
const _statusMap = {
  0: '草稿',
  1: '备案',
  2: '撤回',
  4: '作废',
};

// 发布平台映射
const _publishPlatformMap = {
  0: '国资委官网',
  1: '产权交易中心',
};

// 发布状态映射
const _publishStatusMap = {
  0: '已发布',
  1: '发布失败',
};

// 审核状态映射
const _auditStatusMap = {
  0: '无须审核',
  1: '待审核',
  2: '审核通过',
  3: '审核不通过',
};

// 推送结果映射
const _pushResultMap = {
  0: '推送成功',
  1: '推送失败',
};

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const _formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 格式化日期时间
const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

// 生成随机HTML内容
const generateRandomContent = () => {
  return `<p>招租公告详细内容，包括招租资产的基本情况、招租条件、报名要求等内容。<strong>重要提示：</strong>投标人需在规定时间内参与投标。</p><p>联系电话：0592-${Math.floor(Math.random() * 9000000) + 1000000}</p>`;
};

// 生成随机附件
const generateRandomFiles = () => {
  const fileCount = Math.floor(Math.random() * 4);
  const files = [];
  for (let j = 0; j < fileCount; j++) {
    files.push({
      name: `附件${j+1}.${j % 2 === 0 ? 'docx' : 'pdf'}`,
      url: '#'
    });
  }
  return files;
};

// 生成招租公告信息mock数据
const createNoticeData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    // 生成随机日期
    const createDate = generateRandomDate(new Date(2023, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());
    const publicStartDate = generateRandomDate(createDate, new Date(2023, 11, 31));
    const publicEndDate = generateRandomDate(publicStartDate, new Date(2024, 11, 31));

    // 随机选择所属企业 (1-3个)
    const orgs = [];
    const orgCount = Math.floor(Math.random() * 3) + 1;
    const shuffledOrgs = [...enterpriseOptions].sort(() => 0.5 - Math.random());
    for(let k = 0; k < orgCount; k++) {
      orgs.push(shuffledOrgs[k]);
    }

    // 随机选择管理单位
    const manageUnit = enterpriseOptions[Math.floor(Math.random() * enterpriseOptions.length)];

    // 随机生成状态
    const statusOptions = [0, 1, 2, 4]; // 草稿、备案、撤回、作废
    const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];

    // 随机生成招租方式
    const rentTypeOptions = [0, 1, 2, 5]; // 招租方式
    const rentType = rentTypeOptions[Math.floor(Math.random() * rentTypeOptions.length)];

    // 随机生成发布平台
    const publishPlatform = Math.round(Math.random()); // 0或1

    // 随机生成发布状态
    const publishStatus = Math.round(Math.random()); // 0：已发布，1：发布失败

    // 发布失败原因（仅当发布失败时有值）
    const publishFailReason = publishStatus === 1 ?
      ['接口连接超时', '目标平台返回错误', '数据校验未通过'][Math.floor(Math.random() * 3)] : '';

    // 随机生成审核状态
    const auditStatus = Math.floor(Math.random() * 4); // 0-3

    // 审核不通过原因（仅当审核不通过时有值）
    const auditRejectReason = auditStatus === 3 ? 
      ['材料不完整，请补充', '信息填写有误，请修改', '格式不符合规范', '内容不合规'][Math.floor(Math.random() * 4)] : '';

    // 随机生成推送结果
    const pushResult = Math.round(Math.random()); // 0：推送成功，1：推送失败

    data.push({
      id: i,
      code: `ZZ${String(20000 + i).padStart(6, '0')}`,
      name: `${announcementTitles[i % announcementTitles.length]}(${i})`,
      groupName: '厦门市城市建设发展投资有限公司',
      orgs: orgs,
      manageUnit: manageUnit,
      reportOrNot: Math.round(Math.random()),
      operator: operators[Math.floor(Math.random() * operators.length)],
      rentType: rentType,
      publicStartTime: formatDateTime(publicStartDate),
      publicEndTime: formatDateTime(publicEndDate),
      contentDescription: generateRandomContent(),
      files: generateRandomFiles(),
      status: status,
      publishPlatform: publishPlatform,
      publishStatus: publishStatus,
      publishFailReason: publishFailReason,
      auditStatus: auditStatus,
      auditRejectReason: auditRejectReason,
      pushResult: pushResult,
      entryClerk: operators[Math.floor(Math.random() * operators.length)],
      createTime: formatDateTime(createDate),
      updateTime: formatDateTime(updateDate)
    });
  }

  return data;
};

// 招租公告信息数据
const noticeList = createNoticeData();

export default [
  // 获取招租公告列表
  {
    url: `${baseUrl}/notice/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, limit = 10, name, code, rentType, status, manageUnit, reportOrNot, operator, 
        publicTimeBegin, publicTimeEnd, publishPlatform, publishStatus, auditStatus, auditRejectReason, 
        pushResult, entryClerk, createTimeBegin, createTimeEnd, updateTimeBegin, updateTimeEnd, companies } = query;

      let filteredList = [...noticeList];

      // 根据查询条件过滤
      if (name) {
        filteredList = filteredList.filter(item => item.name.includes(name));
      }
      if (code) {
        filteredList = filteredList.filter(item => item.code.includes(code));
      }
      if (rentType !== undefined && rentType !== '') {
        filteredList = filteredList.filter(item => item.rentType === Number(rentType));
      }
      if (status !== undefined && status !== '') {
        filteredList = filteredList.filter(item => item.status === Number(status));
      }
      if (manageUnit) {
        filteredList = filteredList.filter(item => item.manageUnit === manageUnit);
      }
      if (reportOrNot !== undefined && reportOrNot !== '') {
        filteredList = filteredList.filter(item => item.reportOrNot === Number(reportOrNot));
      }
      if (operator) {
        filteredList = filteredList.filter(item => item.operator.includes(operator));
      }
      if (publishPlatform !== undefined && publishPlatform !== '') {
        filteredList = filteredList.filter(item => item.publishPlatform === Number(publishPlatform));
      }
      if (publishStatus !== undefined && publishStatus !== '') {
        filteredList = filteredList.filter(item => item.publishStatus === Number(publishStatus));
      }
      if (auditStatus !== undefined && auditStatus !== '') {
        filteredList = filteredList.filter(item => item.auditStatus === Number(auditStatus));
      }
      if (auditRejectReason) {
        filteredList = filteredList.filter(item => item.auditRejectReason && item.auditRejectReason.includes(auditRejectReason));
      }
      if (pushResult !== undefined && pushResult !== '') {
        filteredList = filteredList.filter(item => item.pushResult === Number(pushResult));
      }
      if (entryClerk) {
        filteredList = filteredList.filter(item => item.entryClerk.includes(entryClerk));
      }
      
      // 日期范围过滤
      if (publicTimeBegin && publicTimeEnd) {
        filteredList = filteredList.filter(item => {
          const publicTime = new Date(item.publicStartTime).getTime();
          return publicTime >= new Date(publicTimeBegin).getTime() && publicTime <= new Date(publicTimeEnd).getTime();
        });
      }
      if (createTimeBegin && createTimeEnd) {
        filteredList = filteredList.filter(item => {
          const createTime = new Date(item.createTime).getTime();
          return createTime >= new Date(createTimeBegin).getTime() && createTime <= new Date(createTimeEnd).getTime();
        });
      }
      if (updateTimeBegin && updateTimeEnd) {
        filteredList = filteredList.filter(item => {
          const updateTime = new Date(item.updateTime).getTime();
          return updateTime >= new Date(updateTimeBegin).getTime() && updateTime <= new Date(updateTimeEnd).getTime();
        });
      }
      
      // 所属企业过滤
      if (companies && companies.length > 0) {
        const companyArray = typeof companies === 'string' ? companies.split(',') : companies;
        filteredList = filteredList.filter(item => {
          return item.orgs.some(org => companyArray.includes(org));
        });
      }

      return resultPageSuccess(Number(page), Number(limit), filteredList);
    },
  },
  
  // 获取招租公告详情
  {
    url: `${baseUrl}/notice/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const notice = noticeList.find(item => item.id === Number(id));
      if (notice) {
        return resultSuccess(notice);
      } else {
        return resultError('未找到该招租公告信息');
      }
    },
  },
  
  // 新增招租公告
  {
    url: `${baseUrl}/notice/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = noticeList.length + 1;
      const newCode = `ZZ${String(20000 + newId).padStart(6, '0')}`;
      const newNotice = {
        ...body,
        id: newId,
        code: newCode,
        createTime: formatDateTime(new Date()),
        updateTime: formatDateTime(new Date()),
      };
      noticeList.unshift(newNotice);
      return resultSuccess(newNotice);
    },
  },
  
  // 更新招租公告
  {
    url: `${baseUrl}/notice/update`,
    timeout: 200,
    method: 'put',
    response: ({ body }) => {
      const index = noticeList.findIndex(item => item.id === body.id);
      if (index !== -1) {
        noticeList[index] = {
          ...noticeList[index],
          ...body,
          updateTime: formatDateTime(new Date()),
        };
        return resultSuccess(noticeList[index]);
      } else {
        return resultError('未找到该招租公告信息');
      }
    },
  },
  
  // 删除招租公告
  {
    url: `${baseUrl}/notice/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = noticeList.findIndex(item => item.id === Number(id));
      if (index !== -1) {
        const deletedItem = noticeList.splice(index, 1)[0];
        return resultSuccess(deletedItem);
      } else {
        return resultError('未找到该招租公告信息');
      }
    },
  },
  
  // 关联资产包
  {
    url: `${baseUrl}/notice/linkAssets`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const { id, assets } = body;
      const index = noticeList.findIndex(item => item.id === Number(id));
      if (index !== -1) {
        noticeList[index].linkedAssets = assets;
        return resultSuccess(noticeList[index]);
      } else {
        return resultError('未找到该招租公告信息');
      }
    },
  },
  
  // 获取资产包列表（用于关联资产包）
  {
    url: `${baseUrl}/notice/assetOptions`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { keyword } = query;
      const assetCount = Math.floor(Math.random() * 10) + 5;
      const mockAssets = [];
      
      const prefixes = ['商业资产', '企业大区', '企业房', '工业厂房', '商业资产'];
      const locations = ['厦门市思明区', '厦门市湖里区', '厦门市集美区'];
      
      for (let i = 0; i < assetCount; i++) {
        const location = locations[Math.floor(Math.random() * locations.length)];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const assetName = `${location}${prefix}${keyword || ''}资产包`;
        const assetCode = `ZC${new Date().getFullYear()}${String(1000 + i).padStart(4, '0')}`;
        
        mockAssets.push({
          value: `asset_${i}`,
          label: `${assetName} (${assetCode})`
        });
      }
      
      return resultSuccess(mockAssets);
    },
  },
] as MockMethod[]; 