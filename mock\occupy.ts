import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 资产名称列表
const assetNames = [
  '厦门市思明区某地块',
  '厦门市湖里区某办公楼',
  '厦门市集美区某厂房',
  '厦门市海沧区某仓库',
  '厦门市同安区某商业楼',
  '厦门市翔安区某住宅楼',
  '厦门市思明区某商铺',
  '厦门市湖里区某停车场',
  '厦门市集美区某广告牌',
  '厦门市海沧区某设备',
];

// 被占用资产名称列表
const occupyNames = [
  '临时停车场',
  '临时仓库',
  '临时办公场所',
  '临时施工场地',
  '临时堆放场地',
  '临时广告位',
  '临时设备存放',
  '临时人员住宿',
  '临时会议场所',
  '临时展示场地',
];

// 管理单位列表
const manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
];

// 占用人列表
const occupyPersons = [
  '市政建设公司',
  '房地产开发公司',
  '广告传媒公司',
  '物流运输公司',
  '工程施工队',
  '临时租户',
  '政府部门',
  '其他企业',
];

// 占用原因列表
const occupyReasons = [
  '因市政规划需要临时占用',
  '因工程施工需要临时占用',
  '因广告宣传需要临时占用',
  '因仓储物流需要临时占用',
  '因办公需要临时占用',
  '因展示需要临时占用',
  '因其他业务需要临时占用',
];

// 备注列表
const remarks = [
  '占用期间需保持场地整洁',
  '占用期满后需恢复原状',
  '占用期间需注意安全',
  '占用期间需遵守相关规定',
  '占用期间需按时缴纳费用',
  '占用期间需配合管理',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成占用信息mock数据
const createOccupyData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const type = i % 5; // 0-土地, 1-房屋, 2-设备, 3-广告位, 4-其他
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const isChangePurpose = i % 2; // 0-否, 1-是
    const isIllegalConstruction = i % 2; // 0-否, 1-是

    // 生成起始日期和结束日期（确保结束日期晚于起始日期）
    const startDate = generateRandomDate(new Date(2023, 0, 1), new Date());
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 365) + 30); // 至少30天

    // 计算占用天数
    const occupyDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // 随机资产价值（原值）
    const assetsAmount = Number((Math.random() * 1000 + 100).toFixed(2));

    // 随机账面价值（小于等于原值）
    const bookAmount = Number((Math.random() * assetsAmount).toFixed(2));

    // 随机占用面积
    const occupyArea = Number((Math.random() * 1000 + 10).toFixed(2));

    // 创建时间和更新时间
    const createDate = generateRandomDate(new Date(2022, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());

    const dateOfBookValue = generateRandomDate(createDate, new Date());

    // 经办人和录入人
    const operator = operators[i % operators.length];
    const entryClerk = operators[i % operators.length];

    // 生成资产编号
    const companyCode = '0016';
    const now = new Date();
    const yearMonth = now.getFullYear() + ('0' + (now.getMonth() + 1)).slice(-2);
    const serial = i.toString().padStart(5, '0');
    const assetCode = 'ZY' + companyCode + yearMonth + serial;

    data.push({
      id: i,
      name: assetNames[i % assetNames.length] + (i + 1),
      occupyName: occupyNames[i % occupyNames.length] + (i + 1),
      code: assetCode,
      type: type,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      occupyPerson: occupyPersons[i % occupyPersons.length],
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      occupyDays: occupyDays,
      occupyArea: occupyArea,
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(dateOfBookValue),
      isChangePurpose: isChangePurpose,
      isIllegalConstruction: isIllegalConstruction,
      occupyReason: occupyReasons[i % occupyReasons.length],
      status: status,
      remark: Math.random() > 0.7 ? remarks[i % remarks.length] : '',
      operator: operator,
      entryClerk: entryClerk,
      createTime: formatDate(createDate),
      updateTime: formatDate(updateDate),
    });
  }

  return data;
};

const occupyData = createOccupyData();

export default [
  {
    url: `${baseUrl}/occupy/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      const _start = (page - 1) * pageSize;
      const _end = _start + pageSize;
      let filteredData = [...occupyData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter(item => item.name.includes(params.name));
      }
      if (params.code) {
        filteredData = filteredData.filter(item => item.code.includes(params.code));
      }
      if (params.occupyName) {
        filteredData = filteredData.filter(item => item.occupyName.includes(params.occupyName));
      }
      if (params.occupyPerson) {
        filteredData = filteredData.filter(item => item.occupyPerson.includes(params.occupyPerson));
      }
      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter(item => item.status === Number(params.status));
      }
      if (params.type !== undefined && params.type !== '') {
        filteredData = filteredData.filter(item => item.type === Number(params.type));
      }
      if (params.manageUnit && params.manageUnit.length > 0) {
        const units = Array.isArray(params.manageUnit) ? params.manageUnit : [params.manageUnit];
        filteredData = filteredData.filter(item => units.includes(item.manageUnit));
      }
      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter(item => item.reportOrNot === Number(params.reportOrNot));
      }
      if (params.operator) {
        filteredData = filteredData.filter(item => item.operator.includes(params.operator));
      }
      if (params.startDateRange && params.startDateRange.length === 2) {
        filteredData = filteredData.filter(item => 
          item.startDate >= params.startDateRange[0] && item.startDate <= params.startDateRange[1]
        );
      }
      if (params.endDateRange && params.endDateRange.length === 2) {
        filteredData = filteredData.filter(item => 
          item.endDate >= params.endDateRange[0] && item.endDate <= params.endDateRange[1]
        );
      }
      if (params.dateOfBookValueRange && params.dateOfBookValueRange.length === 2) {
        filteredData = filteredData.filter(item => 
          item.dateOfBookValue >= params.dateOfBookValueRange[0] && item.dateOfBookValue <= params.dateOfBookValueRange[1]
        );
      }
      if (params.createTimeRange && params.createTimeRange.length === 2) {
        filteredData = filteredData.filter(item => 
          item.createTime >= params.createTimeRange[0] && item.createTime <= params.createTimeRange[1]
        );
      }
      if (params.minOccupyDays !== undefined && params.minOccupyDays !== '') {
        filteredData = filteredData.filter(item => item.occupyDays >= Number(params.minOccupyDays));
      }
      if (params.maxOccupyDays !== undefined && params.maxOccupyDays !== '') {
        filteredData = filteredData.filter(item => item.occupyDays <= Number(params.maxOccupyDays));
      }
      if (params.minOccupyArea !== undefined && params.minOccupyArea !== '') {
        filteredData = filteredData.filter(item => item.occupyArea >= Number(params.minOccupyArea));
      }
      if (params.maxOccupyArea !== undefined && params.maxOccupyArea !== '') {
        filteredData = filteredData.filter(item => item.occupyArea <= Number(params.maxOccupyArea));
      }
      if (params.minBookAmount !== undefined && params.minBookAmount !== '') {
        filteredData = filteredData.filter(item => item.bookAmount >= Number(params.minBookAmount));
      }
      if (params.maxBookAmount !== undefined && params.maxBookAmount !== '') {
        filteredData = filteredData.filter(item => item.bookAmount <= Number(params.maxBookAmount));
      }
      if (params.isChangePurpose !== undefined && params.isChangePurpose !== '') {
        filteredData = filteredData.filter(item => item.isChangePurpose === Number(params.isChangePurpose));
      }
      if (params.isIllegalConstruction !== undefined && params.isIllegalConstruction !== '') {
        filteredData = filteredData.filter(item => item.isIllegalConstruction === Number(params.isIllegalConstruction));
      }

      return resultPageSuccess(page, pageSize, filteredData);
    },
  },
  {
    url: `${baseUrl}/occupy/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = occupyData.length + 1;
      const newData = {
        ...body,
        id: newId,
        createTime: formatDate(new Date()),
        updateTime: formatDate(new Date()),
      };
      occupyData.push(newData);
      return resultSuccess(newData);
    },
  },
  {
    url: `${baseUrl}/occupy/edit`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const index = occupyData.findIndex(item => item.id === body.id);
      if (index > -1) {
        occupyData[index] = {
          ...occupyData[index],
          ...body,
          updateTime: formatDate(new Date()),
        };
        return resultSuccess(occupyData[index]);
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/occupy/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const index = occupyData.findIndex(item => item.id === Number(query.id));
      if (index > -1) {
        occupyData.splice(index, 1);
        return resultSuccess('删除成功');
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/occupy/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const data = occupyData.find(item => item.id === Number(query.id));
      if (data) {
        return resultSuccess(data);
      }
      return resultError('数据不存在');
    },
  },
  {
    url: `${baseUrl}/occupy/exportXls`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('导出成功');
    },
  },
  {
    url: `${baseUrl}/occupy/exportAll`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('全部导出成功');
    },
  },
  {
    url: `${baseUrl}/occupy/downloadTemplate`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('模板下载成功');
    },
  },
  {
    url: `${baseUrl}/occupy/importExcel`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess('导入成功');
    },
  },
] as MockMethod[]; 