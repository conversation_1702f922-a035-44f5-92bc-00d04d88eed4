import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 其他资产名称列表
const otherAssetNames = [
  '办公家具',
  '电子设备',
  '运输工具',
  '机械设备',
  '通讯设备',
  '安全设备',
  '环保设备',
  '医疗设备',
  '教育设备',
  '体育设备',
  '娱乐设备',
  '厨房设备',
  '清洁设备',
  '维修设备',
  '检测设备',
  '实验设备',
  '展示设备',
  '存储设备',
  '照明设备',
  '通风设备',
];

// 管理单位列表
const _manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
];

// 备注列表
const remarks = [
  '资产运行正常',
  '需要定期维护',
  '建议更新换代',
  '资产老化严重',
  '性能良好',
  '需要维修',
  '闲置状态',
  '出租使用中',
  '自用资产',
  '出借给其他部门',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成其他资产mock数据
const createOtherData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 92; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const companyName = i % 7; // 0-6 对应不同的企业
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是

    // 生成资产使用状态（可能包含多个状态）
    const assetsStatusOptions = [
      [0], [1], [2], [3], [4], [5], [6], [7], [0, 1], [2, 5], [1, 2], [0, 3]
    ];
    const assetsStatus = assetsStatusOptions[i % assetsStatusOptions.length];

    // 根据资产类型生成不同的金额
    let assetsAmount = 0;
    let bookAmount = 0;

    switch (i % 5) {
      case 0: // 办公设备
        assetsAmount = Number((3000 + Math.random() * 10000).toFixed(2));
        break;
      case 1: // 电子设备
        assetsAmount = Number((5000 + Math.random() * 20000).toFixed(2));
        break;
      case 2: // 机械设备
        assetsAmount = Number((15000 + Math.random() * 60000).toFixed(2));
        break;
      case 3: // 运输设备
        assetsAmount = Number((30000 + Math.random() * 150000).toFixed(2));
        break;
      case 4: // 其他设备
        assetsAmount = Number((2000 + Math.random() * 8000).toFixed(2));
        break;
    }

    // 计算账面价值（假设有一定折旧）
    const depreciationRate = Math.random() * 0.3; // 0-30%的折旧率
    bookAmount = Number((assetsAmount * (1 - depreciationRate)).toFixed(2));

    // 生成资产编号
    const companyCode = '0016';
    const now = new Date();
    const yearMonth = now.getFullYear() + ('0' + (now.getMonth() + 1)).slice(-2);
    const serial = i.toString().padStart(5, '0');
    const assetCode = 'QT' + companyCode + yearMonth + serial;

    // 生成日期
    const createDate = generateRandomDate(new Date(2022, 0, 1), new Date());
    const updateDate = generateRandomDate(createDate, new Date());
    const gainDate = generateRandomDate(new Date(2020, 0, 1), createDate);
    const entryDate = generateRandomDate(gainDate, new Date());
    const bookValueDate = generateRandomDate(entryDate, new Date());

    data.push({
      id: i,
      code: assetCode,
      enterpriseCode: 'E' + (20000 + i),
      name: otherAssetNames[i % otherAssetNames.length] + (i + 1),
      groupName: 0, // 集团固定为厦门市城市建设发展投资有限公司
      companyName: companyName,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      province: '福建省',
      city: '厦门市',
      area: ['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'][i % 6],
      address: '厦门市某某路' + (i + 1) + '号',
      status: status,
      assetsStatus: assetsStatus,
      gainDate: formatDate(gainDate),
      assetEntryDate: formatDate(entryDate),
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(bookValueDate),
      remark: Math.random() > 0.7 ? remarks[i % remarks.length] : '',
      operator: operators[i % operators.length],
      entryClerk: operators[i % operators.length],
      createTime: formatDate(createDate),
      updateTime: formatDate(updateDate),
    });
  }

  return data;
};

const otherData = createOtherData();

export default [
  {
    url: `${baseUrl}/other/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      const _start = (page - 1) * pageSize;
      const _end = _start + pageSize;
      let filteredData = [...otherData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.includes(params.name));
      }
      if (params.code) {
        filteredData = filteredData.filter((item) => item.code.includes(params.code));
      }
      if (params.enterpriseCode) {
        filteredData = filteredData.filter((item) => item.enterpriseCode.includes(params.enterpriseCode));
      }
      if (params.companyName !== undefined && params.companyName !== '') {
        filteredData = filteredData.filter((item) => item.companyName === Number(params.companyName));
      }
      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter((item) => item.status === Number(params.status));
      }
      if (params.manageUnit !== undefined && params.manageUnit !== '') {
        filteredData = filteredData.filter((item) => item.manageUnit === Number(params.manageUnit));
      }
      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter((item) => item.reportOrNot === Number(params.reportOrNot));
      }
      if (params.operator) {
        filteredData = filteredData.filter((item) => item.operator.includes(params.operator));
      }
      if (params.entryClerk) {
        filteredData = filteredData.filter((item) => item.entryClerk.includes(params.entryClerk));
      }
      if (params.assetsAmountMin) {
        filteredData = filteredData.filter((item) => item.assetsAmount >= Number(params.assetsAmountMin));
      }
      if (params.assetsAmountMax) {
        filteredData = filteredData.filter((item) => item.assetsAmount <= Number(params.assetsAmountMax));
      }
      if (params.bookAmountMin) {
        filteredData = filteredData.filter((item) => item.bookAmount >= Number(params.bookAmountMin));
      }
      if (params.bookAmountMax) {
        filteredData = filteredData.filter((item) => item.bookAmount <= Number(params.bookAmountMax));
      }

      return resultPageSuccess(page, pageSize, filteredData);
    },
  },
  {
    url: `${baseUrl}/other/add`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const newId = otherData.length + 1;
      const newOther = {
        id: newId,
        ...body,
        createTime: formatDate(new Date()),
        updateTime: formatDate(new Date()),
      };
      otherData.push(newOther);
      return resultSuccess(newOther);
    },
  },
  {
    url: `${baseUrl}/other/edit`,
    timeout: 200,
    method: 'post',
    response: ({ body }) => {
      const index = otherData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        otherData[index] = {
          ...otherData[index],
          ...body,
          updateTime: formatDate(new Date()),
        };
        return resultSuccess(otherData[index]);
      }
      return resultError('其他资产不存在');
    },
  },
  {
    url: `${baseUrl}/other/delete`,
    timeout: 200,
    method: 'delete',
    response: ({ query }) => {
      const index = otherData.findIndex((item) => item.id === Number(query.id));
      if (index !== -1) {
        otherData.splice(index, 1);
        return resultSuccess('删除成功');
      }
      return resultError('其他资产不存在');
    },
  },
  {
    url: `${baseUrl}/other/deleteBatch`,
    timeout: 200,
    method: 'delete',
    response: ({ body }) => {
      const ids = body.ids || [];
      ids.forEach((id) => {
        const index = otherData.findIndex((item) => item.id === id);
        if (index !== -1) {
          otherData.splice(index, 1);
        }
      });
      return resultSuccess('批量删除成功');
    },
  },
  {
    url: `${baseUrl}/other/detail`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const other = otherData.find((item) => item.id === Number(query.id));
      if (other) {
        return resultSuccess(other);
      }
      return resultError('其他资产不存在');
    },
  },
  {
    url: `${baseUrl}/other/importExcel`,
    timeout: 200,
    method: 'post',
    response: () => {
      return resultSuccess('导入成功');
    },
  },
  {
    url: `${baseUrl}/other/exportXls`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('导出成功');
    },
  },
  {
    url: `${baseUrl}/other/exportAll`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('全部导出成功');
    },
  },
  {
    url: `${baseUrl}/other/downloadTemplate`,
    timeout: 200,
    method: 'get',
    response: () => {
      return resultSuccess('模板下载成功');
    },
  },
] as MockMethod[]; 