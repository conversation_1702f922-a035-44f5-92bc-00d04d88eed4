import { MockMethod } from 'vite-plugin-mock';
import { resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 资产名称列表
const assetNames = [
  '厦门市思明区商业用地1号',
  '厦门市湖里区商业大厦2号',
  '厦门市集美区公交站台广告位3号',
  'DELL服务器设备4号',
  '其他资产5号',
  '厦门市翔安区商业用地6号',
  '厦门市思明区商业大厦7号',
  '厦门市湖里区公交站台广告位8号',
  'DELL服务器设备9号',
  '其他资产10号',
  '厦门市海沧区工业用地11号',
  '厦门市同安区办公楼12号',
  '厦门市集美区LED显示屏13号',
  'HP服务器设备14号',
  '其他资产15号',
  '厦门市思明区住宅用地16号',
  '厦门市湖里区商场17号',
  '厦门市翔安区广告牌18号',
  '联想服务器设备19号',
  '其他资产20号',
];



// 备注列表
const remarks = [
  '这是资产ZC10001的备注信息',
  '这是资产ZC10002的备注信息',
  '这是资产ZC10003的备注信息',
  '这是资产ZC10004的备注信息',
  '这是资产ZC10005的备注信息',
  '这是资产ZC10006的备注信息',
  '这是资产ZC10007的备注信息',
  '这是资产ZC10008的备注信息',
  '这是资产ZC10009的备注信息',
  '这是资产ZC10010的备注信息',
];

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// 格式化日期
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 生成资产二维码mock数据
const createQRCodeData = () => {
  const data: any[] = [];

  // 使用固定的示例数据，与原型保持一致
  const fixedData = [
    {
      id: 1,
      code: "ZC10001",
      enterpriseCode: "E02001",
      name: "厦门市思明区商业用地1号",
      assetType: 0,
      groupName: 0,
      companyName: 0,
      manageUnit: 0,
      reportOrNot: 0,
      province: "福建省",
      city: "厦门市",
      area: "思明区",
      address: "福建省厦门市思明区XX路1号",
      status: 0,
      assetsStatus: [0],
      gainDate: "2023-01-15",
      assetEntryDate: "2023-01-30",
      assetsAmount: 100000,
      bookAmount: 90000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10001的备注信息"
    },
    {
      id: 2,
      code: "ZC10002",
      enterpriseCode: "E12002",
      name: "厦门市湖里区商业大厦2号",
      assetType: 1,
      groupName: 1,
      companyName: 1,
      manageUnit: 1,
      reportOrNot: 1,
      province: "福建省",
      city: "厦门市",
      area: "湖里区",
      address: "福建省厦门市湖里区XX路2号",
      status: 1,
      assetsStatus: [1],
      gainDate: "2023-02-15",
      assetEntryDate: "2023-03-01",
      assetsAmount: 200000,
      bookAmount: 180000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10002的备注信息"
    },
    {
      id: 3,
      code: "ZC10003",
      enterpriseCode: "E22003",
      name: "厦门市集美区公交站台广告位3号",
      assetType: 2,
      groupName: 2,
      companyName: 2,
      manageUnit: 2,
      reportOrNot: 0,
      province: "福建省",
      city: "厦门市",
      area: "集美区",
      address: "福建省厦门市集美区XX路3号",
      status: 2,
      assetsStatus: [2],
      gainDate: "2023-03-15",
      assetEntryDate: "2023-04-01",
      assetsAmount: 50000,
      bookAmount: 45000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10003的备注信息"
    },
    {
      id: 4,
      code: "ZC10004",
      enterpriseCode: "E32004",
      name: "DELL服务器设备4号",
      assetType: 3,
      groupName: 3,
      companyName: 3,
      manageUnit: 3,
      reportOrNot: 1,
      province: "福建省",
      city: "厦门市",
      area: "海沧区",
      address: "福建省厦门市海沧区XX路4号",
      status: 4,
      assetsStatus: [3],
      gainDate: "2023-04-15",
      assetEntryDate: "2023-05-01",
      assetsAmount: 30000,
      bookAmount: 27000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10004的备注信息"
    },
    {
      id: 5,
      code: "ZC10005",
      enterpriseCode: "E42005",
      name: "其他资产5号",
      assetType: 4,
      groupName: 4,
      companyName: 4,
      manageUnit: 4,
      reportOrNot: 0,
      province: "福建省",
      city: "厦门市",
      area: "同安区",
      address: "福建省厦门市同安区XX路5号",
      status: 0,
      assetsStatus: [0, 1],
      gainDate: "2023-05-15",
      assetEntryDate: "2023-06-01",
      assetsAmount: 80000,
      bookAmount: 72000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10005的备注信息"
    },
    {
      id: 6,
      code: "ZC10006",
      enterpriseCode: "E52006",
      name: "厦门市翔安区商业用地6号",
      assetType: 0,
      groupName: 5,
      companyName: 5,
      manageUnit: 5,
      reportOrNot: 1,
      province: "福建省",
      city: "厦门市",
      area: "翔安区",
      address: "福建省厦门市翔安区XX路6号",
      status: 1,
      assetsStatus: [0],
      gainDate: "2023-06-15",
      assetEntryDate: "2023-07-01",
      assetsAmount: 150000,
      bookAmount: 135000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10006的备注信息"
    },
    {
      id: 7,
      code: "ZC10007",
      enterpriseCode: "E62007",
      name: "厦门市思明区商业大厦7号",
      assetType: 1,
      groupName: 6,
      companyName: 6,
      manageUnit: 6,
      reportOrNot: 0,
      province: "福建省",
      city: "厦门市",
      area: "思明区",
      address: "福建省厦门市思明区XX路7号",
      status: 1,
      assetsStatus: [2],
      gainDate: "2023-07-15",
      assetEntryDate: "2023-08-01",
      assetsAmount: 250000,
      bookAmount: 225000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10007的备注信息"
    },
    {
      id: 8,
      code: "ZC10008",
      enterpriseCode: "E02008",
      name: "厦门市湖里区公交站台广告位8号",
      assetType: 2,
      groupName: 0,
      companyName: 0,
      manageUnit: 0,
      reportOrNot: 1,
      province: "福建省",
      city: "厦门市",
      area: "湖里区",
      address: "福建省厦门市湖里区XX路8号",
      status: 2,
      assetsStatus: [2],
      gainDate: "2023-08-15",
      assetEntryDate: "2023-09-01",
      assetsAmount: 60000,
      bookAmount: 54000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10008的备注信息"
    },
    {
      id: 9,
      code: "ZC10009",
      enterpriseCode: "E12009",
      name: "DELL服务器设备9号",
      assetType: 3,
      groupName: 1,
      companyName: 1,
      manageUnit: 1,
      reportOrNot: 0,
      province: "福建省",
      city: "厦门市",
      area: "集美区",
      address: "福建省厦门市集美区XX路9号",
      status: 4,
      assetsStatus: [1],
      gainDate: "2023-09-15",
      assetEntryDate: "2023-10-01",
      assetsAmount: 35000,
      bookAmount: 31500,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10009的备注信息"
    },
    {
      id: 10,
      code: "ZC10010",
      enterpriseCode: "E22010",
      name: "其他资产10号",
      assetType: 4,
      groupName: 2,
      companyName: 2,
      manageUnit: 2,
      reportOrNot: 1,
      province: "福建省",
      city: "厦门市",
      area: "海沧区",
      address: "福建省厦门市海沧区XX路10号",
      status: 0,
      assetsStatus: [3, 4],
      gainDate: "2023-10-15",
      assetEntryDate: "2023-11-01",
      assetsAmount: 90000,
      bookAmount: 81000,
      dateOfBookValue: "2023-12-31",
      remark: "这是资产ZC10010的备注信息"
    }
  ];

  // 添加固定数据
  data.push(...fixedData);

  // 生成额外的随机数据以补充到100条
  for (let i = 11; i <= 100; i++) {
    const status = i % 5 === 0 ? 4 : i % 4; // 0-草稿, 1-备案, 2-撤回, 4-作废
    const companyName = i % 7; // 0-6 对应不同的企业
    const manageUnit = i % 7; // 管理单位
    const reportOrNot = i % 2; // 0-否, 1-是
    const assetType = i % 5; // 0-土地, 1-房屋, 2-广告位, 3-设备, 4-其他

    // 生成资产使用状态（可能包含多个状态）
    const assetsStatusOptions = [
      [0], [1], [2], [3], [4], [5], [6], [7], [0, 1], [2, 5], [1, 2], [0, 3]
    ];
    const assetsStatus = assetsStatusOptions[i % assetsStatusOptions.length];

    // 生成日期
    const gainDate = generateRandomDate(new Date('2020-01-01'), new Date('2023-12-31'));
    const assetEntryDate = new Date(gainDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    const dateOfBookValue = generateRandomDate(new Date('2023-01-01'), new Date('2023-12-31'));

    // 生成金额
    const assetsAmount = Math.floor(Math.random() * 1000000) + 10000;
    const bookAmount = assetsAmount * (0.7 + Math.random() * 0.3);

    // 生成位置信息
    const provinces = ['福建省'];
    const cities = ['厦门市', '福州市'];
    const areas = ['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区', '鼓楼区', '台江区', '仓山区', '马尾区', '晋安区'];

    const province = provinces[0];
    const city = cities[i % cities.length];
    const area = areas[i % areas.length];

    const item = {
      id: i,
      code: `ZC${String(i).padStart(5, '0')}`,
      enterpriseCode: `E${String(i % 7).padStart(2, '0')}${String(i).padStart(3, '0')}`,
      name: assetNames[i % assetNames.length],
      assetType: assetType,
      groupName: companyName,
      companyName: companyName,
      manageUnit: manageUnit,
      reportOrNot: reportOrNot,
      province: province,
      city: city,
      area: area,
      address: `${province}${city}${area}XX路${i}号`,
      status: status,
      assetsStatus: assetsStatus,
      gainDate: formatDate(gainDate),
      assetEntryDate: formatDate(assetEntryDate),
      assetsAmount: assetsAmount,
      bookAmount: bookAmount,
      dateOfBookValue: formatDate(dateOfBookValue),
      remark: remarks[i % remarks.length],
      createTime: formatDate(generateRandomDate(new Date('2023-01-01'), new Date('2023-12-31'))),
      updateTime: formatDate(generateRandomDate(new Date('2023-01-01'), new Date('2023-12-31'))),
    };

    data.push(item);
  }

  return data;
};

const qrcodeData = createQRCodeData();

export default [
  {
    url: `${baseUrl}/qrcode/list`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, ...params } = query;
      
      let filteredData = [...qrcodeData];

      // 根据查询条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter(item => 
          item.name.includes(params.name)
        );
      }

      if (params.assetTypes && params.assetTypes.length > 0) {
        filteredData = filteredData.filter(item => 
          params.assetTypes.includes(item.assetType)
        );
      }

      if (params.companyName !== undefined && params.companyName !== '') {
        filteredData = filteredData.filter(item => 
          item.companyName === parseInt(params.companyName)
        );
      }

      if (params.status !== undefined && params.status !== '') {
        filteredData = filteredData.filter(item => 
          item.status === parseInt(params.status)
        );
      }

      if (params.code) {
        filteredData = filteredData.filter(item => 
          item.code.includes(params.code)
        );
      }

      if (params.enterpriseCode) {
        filteredData = filteredData.filter(item => 
          item.enterpriseCode.includes(params.enterpriseCode)
        );
      }

      if (params.manageUnit !== undefined && params.manageUnit !== '') {
        filteredData = filteredData.filter(item => 
          item.manageUnit === parseInt(params.manageUnit)
        );
      }

      if (params.reportOrNot !== undefined && params.reportOrNot !== '') {
        filteredData = filteredData.filter(item => 
          item.reportOrNot === parseInt(params.reportOrNot)
        );
      }

      if (params.address) {
        filteredData = filteredData.filter(item => 
          item.address.includes(params.address)
        );
      }

      if (params.assetsStatus && Array.isArray(params.assetsStatus) && params.assetsStatus.length > 0) {
        filteredData = filteredData.filter(item => 
          params.assetsStatus.some((status: string) => item.assetsStatus.includes(parseInt(status)))
        );
      }

      if (params.gainStartDate && params.gainEndDate) {
        filteredData = filteredData.filter(item => 
          item.gainDate >= params.gainStartDate && item.gainDate <= params.gainEndDate
        );
      }

      if (params.entryStartDate && params.entryEndDate) {
        filteredData = filteredData.filter(item => 
          item.assetEntryDate >= params.entryStartDate && item.assetEntryDate <= params.entryEndDate
        );
      }

      if (params.assetsAmountMin !== undefined && params.assetsAmountMin !== '') {
        filteredData = filteredData.filter(item => 
          item.assetsAmount >= parseFloat(params.assetsAmountMin)
        );
      }

      if (params.assetsAmountMax !== undefined && params.assetsAmountMax !== '') {
        filteredData = filteredData.filter(item => 
          item.assetsAmount <= parseFloat(params.assetsAmountMax)
        );
      }

      // 分页
      const startIndex = (parseInt(page) - 1) * parseInt(pageSize);
      const endIndex = startIndex + parseInt(pageSize);
      const paginatedData = filteredData.slice(startIndex, endIndex);

      return resultPageSuccess(parseInt(page), parseInt(pageSize), filteredData);
    },
  },
  {
    url: `${baseUrl}/qrcode/exportXls`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出功能
      return resultSuccess('导出成功');
    },
  },
  {
    url: `${baseUrl}/qrcode/exportAll`,
    timeout: 200,
    method: 'get',
    response: ({ query }) => {
      // 模拟全部导出功能
      return resultSuccess('全部导出成功');
    },
  },
] as MockMethod[]; 