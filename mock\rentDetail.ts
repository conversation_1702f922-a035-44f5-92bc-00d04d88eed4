import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, baseUrl } from './_util';

// 资产类型映射
const assetTypeMap = {
  land: '土地',
  house: '房屋',
  ad: '广告位',
  equipment: '设备',
  other: '其他',
};

// 出租方式映射
const rentMethodMap = {
  xiamen_public: '厦门公开招租（进场）',
  other_place_public: '异地公开招租（进场）',
  public_not_enter: '公开招租（非进场）',
  other_rent: '其他招租',
  public_renew: '公开招租（续租）',
  other_professional: '其他方式招租（专业化招商）',
  other_agreement: '其他方式招租（协议）',
};

// 企业名称数组
const companyNames = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 生成租金明细数据
const generateRentDetailData = (count = 50) => {
  const data = [];
  const assetTypes = Object.keys(assetTypeMap);
  const rentMethods = Object.keys(rentMethodMap);
  const quarters = ['Q1', 'Q2', 'Q3', 'Q4'];
  
  for (let i = 1; i <= count; i++) {
    const assetType = assetTypes[Math.floor(Math.random() * assetTypes.length)];
    const rentMethod = rentMethods[Math.floor(Math.random() * rentMethods.length)];
    const year = Math.random() > 0.5 ? '2023' : '2024';
    const quarter = quarters[Math.floor(Math.random() * quarters.length)];
    
    // 随机选择所属企业
    const companyName = companyNames[Math.floor(Math.random() * companyNames.length)];
    
    // 计算租金相关数据
    const totalRent = parseFloat((Math.random() * 1000 + 100).toFixed(2));
    const expectedRent = parseFloat((totalRent / 4).toFixed(2));
    const actualRent = parseFloat((expectedRent * (Math.random() * 0.3 + 0.7)).toFixed(2));
    const unpaidRent = parseFloat((expectedRent - actualRent).toFixed(2));
    const penaltyRent = parseFloat((unpaidRent * (Math.random() * 0.1)).toFixed(2));
    const discountRent = parseFloat((unpaidRent * (Math.random() * 0.2)).toFixed(2));
    
    // 生成日期
    const dealYear = parseInt(year) - (Math.random() > 0.7 ? 1 : 0);
    const dealMonth = Math.floor(Math.random() * 12) + 1;
    const dealDay = Math.floor(Math.random() * 28) + 1;
    const dealDate = `${dealYear}-${dealMonth.toString().padStart(2, '0')}-${dealDay.toString().padStart(2, '0')}`;
    
    // 租期相关
    const leaseStartYear = dealYear;
    const leaseStartMonth = dealMonth;
    const leaseStartDay = dealDay;
    const leaseStartDate = `${leaseStartYear}-${leaseStartMonth.toString().padStart(2, '0')}-${leaseStartDay.toString().padStart(2, '0')}`;
    
    const leaseEndYear = leaseStartYear + Math.floor(Math.random() * 5) + 1;
    const leaseEndMonth = Math.floor(Math.random() * 12) + 1;
    const leaseEndDay = Math.floor(Math.random() * 28) + 1;
    const leaseEndDate = `${leaseEndYear}-${leaseEndMonth.toString().padStart(2, '0')}-${leaseEndDay.toString().padStart(2, '0')}`;
    
    data.push({
      id: i,
      targetName: `${assetTypeMap[assetType]}标的${i.toString().padStart(3, '0')}`,
      assetType: assetTypeMap[assetType],
      area: parseFloat((Math.random() * 5000 + 100).toFixed(2)),
      dealDate: dealDate,
      leaseStartDate: leaseStartDate,
      leaseEndDate: leaseEndDate,
      totalRent: totalRent,
      year: year,
      quarter: quarter,
      expectedRent: expectedRent,
      actualRent: actualRent,
      unpaidRent: unpaidRent,
      penaltyRent: penaltyRent,
      discountRent: discountRent,
      companyName: companyName,
      packageName: `${rentMethodMap[rentMethod]}资产包${Math.floor(Math.random() * 10) + 1}`,
      packageCode: `ZC${year}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`
    });
  }
  
  return data;
};

// 模拟数据库
const mockDb = {
  rentDetailList: generateRentDetailData(50)
};

// 根据筛选条件过滤数据
const filterRentDetailData = (data, params) => {
  return data.filter(item => {
    // 资产类型筛选
    if (params.assetType && assetTypeMap[params.assetType] !== item.assetType) {
      return false;
    }
    
    // 出租方式筛选
    if (params.rentMethod && !item.packageName.includes(rentMethodMap[params.rentMethod])) {
      return false;
    }
    
    // 标的名称筛选
    if (params.targetName && !item.targetName.includes(params.targetName)) {
      return false;
    }
    
    // 所属企业筛选
    if (params.companyName && params.companyName.length > 0) {
      // 获取企业名称文本数组
      const selectedCompanies = params.companyName.map(value => companyNames[value]);
      if (!selectedCompanies.includes(item.companyName)) {
        return false;
      }
    }
    
    // 资产包名称筛选
    if (params.packageName && !item.packageName.includes(params.packageName)) {
      return false;
    }
    
    // 资产包编号筛选
    if (params.packageCode && !item.packageCode.includes(params.packageCode)) {
      return false;
    }
    
    // 年份-季度范围筛选
    if (params.startYear && params.startQuarter && params.endYear && params.endQuarter) {
      const startYear = parseInt(params.startYear);
      const endYear = parseInt(params.endYear);
      const itemYear = parseInt(item.year);
      
      // 年份比较
      if (itemYear < startYear || itemYear > endYear) {
        return false;
      }
      
      // 如果是起始年份，还需要比较季度
      if (itemYear === startYear) {
        const quarterValue = {
          'Q1': 1,
          'Q2': 2,
          'Q3': 3,
          'Q4': 4
        };
        
        if (quarterValue[item.quarter] < quarterValue[params.startQuarter]) {
          return false;
        }
      }
      
      // 如果是结束年份，还需要比较季度
      if (itemYear === endYear) {
        const quarterValue = {
          'Q1': 1,
          'Q2': 2,
          'Q3': 3,
          'Q4': 4
        };
        
        if (quarterValue[item.quarter] > quarterValue[params.endQuarter]) {
          return false;
        }
      }
    }
    
    return true;
  });
};

// 计算合计行数据
const calculateSummary = (data) => {
  // 数值列定义
  const numericColumns = ['area', 'totalRent', 'expectedRent', 'actualRent', 'unpaidRent', 'penaltyRent', 'discountRent'];
  
  // 当前页合计
  const currentPageSummary = {
    isSummary: true,
    summaryType: '当前页合计',
    targetName: '当前页合计',
    id: -1
  };
  
  // 总合计
  const totalSummary = {
    isSummary: true,
    summaryType: '总合计',
    targetName: '总合计',
    id: -2
  };
  
  // 计算数值列合计
  numericColumns.forEach(columnProp => {
    // 当前页合计
    let pageSum = 0;
    data.forEach(row => {
      const value = parseFloat(row[columnProp]);
      if (!isNaN(value)) {
        pageSum += value;
      }
    });
    
    currentPageSummary[columnProp] = pageSum;
    
    // 全部数据合计（这里使用模拟的总计值，实际项目中应该从后端获取）
    // 简单起见，我们将总合计模拟为当前页的5倍
    totalSummary[columnProp] = pageSum * 5;
  });
  
  return [currentPageSummary, totalSummary];
};

export default [
  // 获取租金明细列表
  {
    url: `${baseUrl}/rentDetail/list`,
    method: 'get',
    response: ({ query }) => {
      const { pageNo = 1, pageSize = 10 } = query;
      
      // 根据条件筛选数据
      const filteredData = filterRentDetailData(mockDb.rentDetailList, query);
      
      // 分页
      const startIndex = (pageNo - 1) * pageSize;
      const endIndex = startIndex + parseInt(pageSize);
      const pageData = filteredData.slice(startIndex, endIndex);
      
      // 计算合计行
      const summary = calculateSummary(pageData);
      
      return resultSuccess({
        records: pageData,
        summary: summary,
        total: filteredData.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 导出选中数据
  {
    url: `${baseUrl}/rentDetail/exportXls`,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      
      if (!ids) {
        return resultSuccess({
          success: false,
          message: '未选择要导出的数据'
        });
      }
      
      const idArray = ids.split(',').map(id => parseInt(id));
      const exportData = mockDb.rentDetailList.filter(item => idArray.includes(item.id));
      
      return resultSuccess({
        success: true,
        message: `成功导出${exportData.length}条数据`
      });
    },
  },
  
  // 导出全部数据
  {
    url: `${baseUrl}/rentDetail/exportAll`,
    method: 'get',
    response: ({ query }) => {
      // 根据条件筛选数据
      const filteredData = filterRentDetailData(mockDb.rentDetailList, query);
      
      return resultSuccess({
        success: true,
        message: `成功导出${filteredData.length}条数据`
      });
    },
  },
] as MockMethod[]; 