import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 模拟数据库
const mockDb = {
  rentInfoList: (() => {
    const result = [];
    for (let i = 0; i < 20; i++) {
      result.push({
        id: i + 1,
        code: `ZL${100000 + i}`,
        name: `租赁资产包${i + 1}`,
        rentType: Random.integer(0, 6),
        merchantsType: Random.integer(0, 1),
        manageUnit: Random.integer(0, 6),
        reportOrNot: Random.integer(0, 1),
        operator: Random.cname(),
        entryClerk: Random.cname(),
        createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
        status: Random.integer(0, 4),
        listingPrice: Random.float(1000, 100000, 2, 2),
        priceUnit: Random.integer(0, 4),
        lease: `${Random.integer(1, 10)}年`,
        rentArea: Random.float(100, 50000, 2, 2),
        lessorName: `厦门${Random.cword(2, 4)}有限公司`,
        listingStartDate: Random.date('yyyy-MM-dd'),
        listingEndDate: Random.date('yyyy-MM-dd'),
      });
    }
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/rentInfo/list`,
    method: 'get',
    response: ({ query }) => {
      const { pageNo = 1, pageSize = 10, name, code, status } = query;
      
      let list = [...mockDb.rentInfoList];
      
      // 条件筛选
      if (name) {
        list = list.filter(item => item.name.includes(name));
      }
      
      if (code) {
        list = list.filter(item => item.code.includes(code));
      }
      
      if (status !== undefined && status !== null && status !== '') {
        list = list.filter(item => item.status === parseInt(status));
      }
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 获取详情
  {
    url: `${baseUrl}/rentInfo/detail`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      
      // 查找对应ID的记录
      const item = mockDb.rentInfoList.find(item => item.id === parseInt(id));
      
      if (!item) {
        return resultError('未找到对应记录');
      }
      
      // 补充详情信息
      const detail = {
        ...item,
        relatedPublicLeasing: item.rentType === 4 ? 'ZZ2023002' : '',
        remark: '这是一个测试资产包',
        listingOrg: item.rentType === 1 ? '厦门产权交易中心' : '',
        listingLocation: item.rentType === 1 ? 'fujian,xiamen' : '',
        lessorType: Random.integer(0, 1),
        address: '福建省厦门市思明区',
        registration: '厦门市思明区会展路123号',
        registeredCapital: Random.float(1000, 100000, 2, 2),
        economicType: Random.integer(0, 6),
        legalRepresentative: Random.cname(),
        industry: '建筑业',
        contentDescription: '这是一个位于厦门市中心的优质商业资产包，包含多个临街商铺和办公空间。',
        fileList: [
          {
            uid: 'file-1',
            name: '资产包介绍.pdf',
            status: 'done',
            url: 'https://example.com/files/intro.pdf'
          }
        ],
        associatedAssets: [
          {
            assetType: 1,
            assetsCode: 'ZC005',
            targetName: '中山路临街商铺',
            area: 300
          },
          {
            assetType: 0,
            assetsCode: 'ZC006',
            targetName: '城南停车场地块',
            area: 25000
          }
        ],
        transactions: [
          {
            targetName: '中山路临街商铺',
            dealStatus: 1,
            targetArea: 300,
            dealName: '厦门市某某商贸有限公司',
            dealPrice: 50,
            dealUnit: 2,
            dealDate: '2023-08-15',
            dealBeginDate: '2023-09-01',
            dealEndDate: '2026-08-31',
            dealInternal: 0,
            dealCode: ['GG2023001'],
            planTotalRent: 180,
            rentalIncome: 60,
            rentEscalationRate: '5%',
            contractFile: [
              {
                uid: 'contract-1',
                name: '租赁合同.pdf',
                status: 'done',
                url: 'https://example.com/files/contract.pdf'
              }
            ],
            remark: '首次租赁',
            rentDetails: [
              {
                year: '2023',
                season: 3,
                receivableRent: 15,
                receiptsRent: 15,
                unpaidRent: 0,
                defaultRent: 0,
                reductionRent: 0,
                reason: 6,
                detailsRemark: ''
              },
              {
                year: '2023',
                season: 4,
                receivableRent: 15,
                receiptsRent: 15,
                unpaidRent: 0,
                defaultRent: 0,
                reductionRent: 0,
                reason: 6,
                detailsRemark: ''
              }
            ]
          },
          {
            targetName: '城南停车场地块',
            dealStatus: 0,
            targetArea: null,
            dealName: '',
            dealPrice: null,
            dealUnit: 0,
            dealDate: '',
            dealBeginDate: '',
            dealEndDate: '',
            dealInternal: 0,
            dealCode: [],
            planTotalRent: null,
            rentalIncome: null,
            rentEscalationRate: '',
            contractFile: [],
            remark: '流拍',
            rentDetails: []
          }
        ]
      };
      
      return resultSuccess(detail);
    },
  },
  
  // 保存
  {
    url: `${baseUrl}/rentInfo/save`,
    method: 'post',
    response: ({ body }) => {
      const id = mockDb.rentInfoList.length + 1;
      const code = `ZL${100000 + id}`;
      
      const newItem = {
        ...body,
        id,
        code,
        createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      };
      
      mockDb.rentInfoList.push(newItem);
      
      return resultSuccess({
        id,
        code,
      });
    },
  },
  
  // 更新
  {
    url: `${baseUrl}/rentInfo/update`,
    method: 'post',
    response: ({ body }) => {
      const { id } = body;
      
      const index = mockDb.rentInfoList.findIndex(item => item.id === id);
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.rentInfoList[index] = {
        ...mockDb.rentInfoList[index],
        ...body,
      };
      
      return resultSuccess(true);
    },
  },
  
  // 删除
  {
    url: `${baseUrl}/rentInfo/delete`,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      
      const index = mockDb.rentInfoList.findIndex(item => item.id === parseInt(id));
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.rentInfoList.splice(index, 1);
      
      return resultSuccess(true);
    },
  },
] as MockMethod[]; 