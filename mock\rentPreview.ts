import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';

// 模拟租金数据
const mockRentData = [
  // 2023年数据
  {
    year: '2023',
    quarter: '一季度',
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 456.45,
    paidRental: 432.23,
    unpaidRental: 24.22,
    professionalRental: 320.15,
    nonProfessionalRental: 112.08,
    xiamenPublicRental: 220.20,
    otherPlacePublicRental: 85.80,
    nonEntryPublicRental: 76.30,
    otherMethodRental: 50.93,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    year: '2023',
    quarter: '二季度',
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 400.00,
    paidRental: 350.00,
    unpaidRental: 50.00,
    professionalRental: 200.00,
    nonProfessionalRental: 150.00,
    xiamenPublicRental: 130.00,
    otherPlacePublicRental: 60.00,
    nonEntryPublicRental: 110.00,
    otherMethodRental: 50.00,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    year: '2023',
    quarter: '三季度',
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 425.78,
    paidRental: 385.45,
    unpaidRental: 40.33,
    professionalRental: 280.45,
    nonProfessionalRental: 105.00,
    xiamenPublicRental: 175.20,
    otherPlacePublicRental: 80.70,
    nonEntryPublicRental: 79.55,
    otherMethodRental: 50.00,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    year: '2023',
    quarter: '四季度',
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 500.00,
    paidRental: 440.00,
    unpaidRental: 60.00,
    professionalRental: 300.00,
    nonProfessionalRental: 140.00,
    xiamenPublicRental: 200.00,
    otherPlacePublicRental: 100.00,
    nonEntryPublicRental: 90.00,
    otherMethodRental: 50.00,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    year: '2023',
    quarter: '一季度',
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    expectedRental: 220.50,
    paidRental: 200.30,
    unpaidRental: 20.20,
    professionalRental: 150.20,
    nonProfessionalRental: 50.10,
    xiamenPublicRental: 100.40,
    otherPlacePublicRental: 40.30,
    nonEntryPublicRental: 45.60,
    otherMethodRental: 14.00,
    companyName: '厦门市地热资源管理有限公司'
  },
  {
    year: '2023',
    quarter: '二季度',
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    expectedRental: 200.00,
    paidRental: 180.00,
    unpaidRental: 20.00,
    professionalRental: 100.00,
    nonProfessionalRental: 80.00,
    xiamenPublicRental: 80.00,
    otherPlacePublicRental: 30.00,
    nonEntryPublicRental: 40.00,
    otherMethodRental: 30.00,
    companyName: '厦门市地热资源管理有限公司'
  },
  {
    year: '2023',
    quarter: '三季度',
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    expectedRental: 230.00,
    paidRental: 200.80,
    unpaidRental: 29.20,
    professionalRental: 135.30,
    nonProfessionalRental: 65.50,
    xiamenPublicRental: 95.40,
    otherPlacePublicRental: 35.80,
    nonEntryPublicRental: 44.60,
    otherMethodRental: 25.00,
    companyName: '厦门市地热资源管理有限公司'
  },
  {
    year: '2023',
    quarter: '四季度',
    assetType: '土地',
    propertyRight: 'no',
    propertyRightText: '无产权',
    expectedRental: 220.00,
    paidRental: 190.00,
    unpaidRental: 30.00,
    professionalRental: 130.00,
    nonProfessionalRental: 60.00,
    xiamenPublicRental: 100.00,
    otherPlacePublicRental: 30.00,
    nonEntryPublicRental: 40.00,
    otherMethodRental: 20.00,
    companyName: '厦门市地热资源管理有限公司'
  },
  {
    year: '2023',
    quarter: '一季度',
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 656.80,
    paidRental: 600.20,
    unpaidRental: 56.60,
    professionalRental: 400.50,
    nonProfessionalRental: 199.70,
    xiamenPublicRental: 305.30,
    otherPlacePublicRental: 95.20,
    nonEntryPublicRental: 170.80,
    otherMethodRental: 28.90,
    companyName: '厦门地丰置业有限公司'
  },
  {
    year: '2023',
    quarter: '二季度',
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 600.00,
    paidRental: 550.00,
    unpaidRental: 50.00,
    professionalRental: 380.00,
    nonProfessionalRental: 170.00,
    xiamenPublicRental: 280.00,
    otherPlacePublicRental: 100.00,
    nonEntryPublicRental: 140.00,
    otherMethodRental: 30.00,
    companyName: '厦门地丰置业有限公司'
  },
  {
    year: '2023',
    quarter: '三季度',
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 620.45,
    paidRental: 580.30,
    unpaidRental: 40.15,
    professionalRental: 395.40,
    nonProfessionalRental: 184.90,
    xiamenPublicRental: 285.20,
    otherPlacePublicRental: 95.10,
    nonEntryPublicRental: 175.80,
    otherMethodRental: 24.20,
    companyName: '厦门地丰置业有限公司'
  },
  {
    year: '2023',
    quarter: '四季度',
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 700.00,
    paidRental: 600.00,
    unpaidRental: 100.00,
    professionalRental: 400.00,
    nonProfessionalRental: 200.00,
    xiamenPublicRental: 320.00,
    otherPlacePublicRental: 95.00,
    nonEntryPublicRental: 150.00,
    otherMethodRental: 35.00,
    companyName: '厦门地丰置业有限公司'
  },
  {
    year: '2023',
    quarter: '一季度',
    assetType: '广告位',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 60.50,
    paidRental: 58.80,
    unpaidRental: 1.70,
    professionalRental: 40.40,
    nonProfessionalRental: 18.40,
    xiamenPublicRental: 30.20,
    otherPlacePublicRental: 10.30,
    nonEntryPublicRental: 13.10,
    otherMethodRental: 5.20,
    companyName: '图智策划咨询（厦门）有限公司'
  },
  {
    year: '2023',
    quarter: '二季度',
    assetType: '广告位',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 60.00,
    paidRental: 60.00,
    unpaidRental: 0.00,
    professionalRental: 45.00,
    nonProfessionalRental: 15.00,
    xiamenPublicRental: 35.00,
    otherPlacePublicRental: 10.00,
    nonEntryPublicRental: 12.00,
    otherMethodRental: 3.00,
    companyName: '图智策划咨询（厦门）有限公司'
  },
  {
    year: '2023',
    quarter: '一季度',
    assetType: '设备',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 42.60,
    paidRental: 42.60,
    unpaidRental: 0.00,
    professionalRental: 32.70,
    nonProfessionalRental: 9.90,
    xiamenPublicRental: 22.75,
    otherPlacePublicRental: 9.95,
    nonEntryPublicRental: 7.60,
    otherMethodRental: 2.30,
    companyName: '厦门市集众祥和物业管理有限公司'
  },
  {
    year: '2023',
    quarter: '二季度',
    assetType: '设备',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 42.60,
    paidRental: 42.60,
    unpaidRental: 0.00,
    professionalRental: 32.70,
    nonProfessionalRental: 9.90,
    xiamenPublicRental: 22.75,
    otherPlacePublicRental: 9.95,
    nonEntryPublicRental: 7.60,
    otherMethodRental: 2.30,
    companyName: '厦门市集众祥和物业管理有限公司'
  },
  // 2024年数据
  {
    year: '2024',
    quarter: '一季度',
    assetType: '土地',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 460.80,
    paidRental: 390.50,
    unpaidRental: 70.30,
    professionalRental: 265.30,
    nonProfessionalRental: 125.20,
    xiamenPublicRental: 195.20,
    otherPlacePublicRental: 70.10,
    nonEntryPublicRental: 85.40,
    otherMethodRental: 39.80,
    companyName: '厦门市城市建设发展投资有限公司'
  },
  {
    year: '2024',
    quarter: '一季度',
    assetType: '房屋',
    propertyRight: 'yes',
    propertyRightText: '有产权',
    expectedRental: 650.40,
    paidRental: 590.30,
    unpaidRental: 60.10,
    professionalRental: 410.20,
    nonProfessionalRental: 180.10,
    xiamenPublicRental: 320.50,
    otherPlacePublicRental: 90.80,
    nonEntryPublicRental: 150.30,
    otherMethodRental: 28.70,
    companyName: '厦门地丰置业有限公司'
  },
  {
    year: '2024',
    quarter: '一季度',
    assetType: '土地',
    propertyRight: 'agent',
    propertyRightText: '代管',
    expectedRental: 180.50,
    paidRental: 165.30,
    unpaidRental: 15.20,
    professionalRental: 120.40,
    nonProfessionalRental: 44.90,
    xiamenPublicRental: 85.20,
    otherPlacePublicRental: 35.10,
    nonEntryPublicRental: 30.00,
    otherMethodRental: 15.00,
    companyName: '厦门兴地房屋征迁服务有限公司'
  },
  {
    year: '2024',
    quarter: '一季度',
    assetType: '房屋',
    propertyRight: 'no',
    propertyRightText: '无产权',
    expectedRental: 280.60,
    paidRental: 250.40,
    unpaidRental: 30.20,
    professionalRental: 180.30,
    nonProfessionalRental: 70.10,
    xiamenPublicRental: 120.50,
    otherPlacePublicRental: 45.20,
    nonEntryPublicRental: 60.70,
    otherMethodRental: 24.00,
    companyName: '厦门市人居乐业物业服务有限公司'
  }
];

export default [
  // 获取租金一览表数据
  {
    url: `${baseUrl}/rentPreview/list`,
    method: 'get',
    response: ({ query }) => {
      const { yearRange, quarters, assetTypes, propertyRights, companies } = query;

      let list = [...mockRentData];

      // 年份范围筛选
      if (yearRange && yearRange.length === 2) {
        const start = parseInt(yearRange[0]);
        const end = parseInt(yearRange[1]);
        list = list.filter(item => {
          const itemYear = parseInt(item.year);
          return itemYear >= start && itemYear <= end;
        });
      }
      
      // 季度筛选
      if (quarters && quarters.length > 0) {
        const quarterMap = {
          'Q1': '一季度',
          'Q2': '二季度',
          'Q3': '三季度',
          'Q4': '四季度'
        };
        
        const quartersArray = Array.isArray(quarters) ? quarters : [quarters];
        const selectedQuarters = quartersArray.map(q => quarterMap[q]);
        
        list = list.filter(item => selectedQuarters.includes(item.quarter));
      }
      
      // 资产类型筛选
      if (assetTypes && assetTypes.length > 0) {
        const assetTypeMap = {
          'land': '土地',
          'house': '房屋',
          'ad': '广告位',
          'equipment': '设备',
          'other': '其他'
        };
        
        const assetTypesArray = Array.isArray(assetTypes) ? assetTypes : [assetTypes];
        const selectedAssetTypes = assetTypesArray.map(t => assetTypeMap[t]);
        
        list = list.filter(item => selectedAssetTypes.includes(item.assetType));
      }
      
      // 产权状况筛选
      if (propertyRights && propertyRights.length > 0) {
        const propertyRightsArray = Array.isArray(propertyRights) ? propertyRights : [propertyRights];
        list = list.filter(item => propertyRightsArray.includes(item.propertyRight));
      }
      
      // 所属企业筛选
      if (companies && companies.length > 0) {
        const companiesArray = Array.isArray(companies) ? companies : [companies];
        list = list.filter(item => item.companyName && companiesArray.includes(item.companyName));
      }
      
      return resultSuccess(list);
    },
  },
  
  // 导出租金一览表数据
  {
    url: `${baseUrl}/rentPreview/export`,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出功能，实际应该返回文件流
      return resultSuccess('导出成功');
    },
  },
] as MockMethod[];
