import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 模拟数据库
const mockDb = {
  reportWarnList: (() => {
    const result = [];
    for (let i = 0; i < 65; i++) {
      // 生成随机时间
      const generateRandomDateTime = () => {
        const year = 2022 + Math.floor(Math.random() * 2);
        const month = Math.floor(Math.random() * 12) + 1;
        const day = Math.floor(Math.random() * 28) + 1;
        const hour = Math.floor(Math.random() * 24);
        const minute = Math.floor(Math.random() * 60);
        const second = Math.floor(Math.random() * 60);
        
        return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:${String(second).padStart(2, '0')}`;
      };

      // 随机数量数据
      const assetCount = Math.floor(Math.random() * 500) + 100;
      const selfUseCount = Math.floor(Math.random() * 200) + 50;
      const rentCount = Math.floor(Math.random() * 100) + 30;
      const idleCount = Math.floor(Math.random() * 80) + 20;
      const occupyCount = Math.floor(Math.random() * 50) + 10;
      const borrowCount = Math.floor(Math.random() * 40) + 5;
      const transferCount = Math.floor(Math.random() * 30) + 1;

      result.push({
        id: 10000 + i,
        assetInfo: generateRandomDateTime(),
        assetCount: assetCount,
        selfUse: generateRandomDateTime(),
        selfUseCount: selfUseCount,
        rent: generateRandomDateTime(),
        rentCount: rentCount,
        idle: generateRandomDateTime(),
        idleCount: idleCount,
        occupy: generateRandomDateTime(),
        occupyCount: occupyCount,
        borrow: generateRandomDateTime(),
        borrowCount: borrowCount,
        transfer: generateRandomDateTime(),
        transferCount: transferCount,
        createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
        updateTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
      });
    }
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/reportWarn/list`,
    method: 'get',
    response: ({ query }) => {
      const { pageNo = 1, pageSize = 10 } = query;
      
      let list = [...mockDb.reportWarnList];
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 导出
  {
    url: `${baseUrl}/reportWarn/export`,
    method: 'get',
    response: ({ query }) => {
      // 模拟导出成功
      return resultSuccess({
        message: '导出成功',
        url: '/mock/files/report-warn-export.xlsx'
      });
    },
  },
  
  // 全部导出
  {
    url: `${baseUrl}/reportWarn/exportAll`,
    method: 'get',
    response: ({ query }) => {
      // 模拟全部导出成功
      return resultSuccess({
        message: '全部导出成功',
        url: '/mock/files/report-warn-export-all.xlsx'
      });
    },
  },
] as MockMethod[];
