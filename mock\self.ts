import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from './_util';

// 资产名称列表
const assetNames = [
  '厦门市思明区某地块',
  '厦门市湖里区某办公楼',
  '厦门市集美区某厂房',
  '厦门市海沧区某仓库',
  '厦门市同安区某商业楼',
  '厦门市翔安区某住宅楼',
  '厦门市思明区某商铺',
  '厦门市湖里区某停车场',
  '厦门市集美区某广告牌',
  '厦门市海沧区某设备',
];

// 被占用资产名称列表
const occupyNames = [
  '临时停车场',
  '临时仓库',
  '临时办公场所',
  '临时施工场地',
  '临时堆放场地',
  '临时广告位',
  '临时设备存放',
  '临时人员住宿',
  '临时会议场所',
  '临时展示场地',
];

// 管理单位列表
const manageUnits = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
];

// 经办人和录入人列表
const operators = ['张建国', '李明华', '王志强', '陈文博', '刘德华', '赵敏', '孙志远', '周晓明', '吴建设', '郑海峰'];

// 使用用途列表
const purposes = [
  '日常办公',
  '科研实验',
  '教学使用',
  '员工住宿',
  '会议场所',
  '展览展示',
  '设备放置',
  '档案存储',
  '临时仓库',
  '业务接待',
];

// 备注列表
const remarks = [
  '自用期间需保持场地整洁',
  '自用期满后需归还原状',
  '自用期间需注意安全',
  '自用期间需遵守相关规定',
  '自用期间需定期维护',
  '自用期间需配合管理',
  '自用期间产生的费用由使用单位承担',
  '自用期间需按时报告使用情况',
  '自用期间需接受监督检查',
  '自用期间需确保资产安全',
];

// 生成随机日期
function getRandomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// 格式化日期
function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

// 格式化日期时间
function formatDateTime(date: Date): string {
  return date.toISOString().replace('T', ' ').split('.')[0];
}

// 生成模拟数据
function generateSelfData(count: number = 100) {
  const data: any[] = [];
  
  for (let i = 1; i <= count; i++) {
    const type = Math.floor(Math.random() * 5); // 0-4
    const assetName = assetNames[Math.floor(Math.random() * assetNames.length)];
    const occupyName = Math.random() > 0.5 ? occupyNames[Math.floor(Math.random() * occupyNames.length)] : '';
    const manageUnit = manageUnits[Math.floor(Math.random() * manageUnits.length)];
    const operator = operators[Math.floor(Math.random() * operators.length)];
    const entryClerk = operators[Math.floor(Math.random() * operators.length)];
    const purpose = purposes[Math.floor(Math.random() * purposes.length)];
    const remark = Math.random() > 0.3 ? remarks[Math.floor(Math.random() * remarks.length)] : '';
    
    // 生成起始日期和结束日期
    const startDate = getRandomDate(new Date(2023, 0, 1), new Date(2023, 11, 31));
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 365) + 30); // 至少30天
    
    // 计算自用天数
    const selfDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // 账面价值时点（在自用起始日期之前）
    const dateOfBookValue = new Date(startDate);
    dateOfBookValue.setDate(dateOfBookValue.getDate() - Math.floor(Math.random() * 90));
    
    // 生成金额和面积
    const assetsAmount = +(Math.random() * 1000 + 100).toFixed(2);
    const bookAmount = +(Math.random() * assetsAmount).toFixed(2);
    const selfArea = type === 0 || type === 1 ? +(Math.random() * 1000 + 10).toFixed(2) : null;
    
    // 创建时间和更新时间
    const createTime = getRandomDate(new Date(2023, 0, 1), new Date());
    const updateTime = new Date(createTime);
    updateTime.setDate(updateTime.getDate() + Math.floor(Math.random() * 30));
    
    // 状态
    const status = Math.floor(Math.random() * 4); // 0, 1, 2, 4
    const statusMap = [0, 1, 2, 4];
    
    data.push({
      id: 10000 + i,
      type,
      name: `${assetName}${i}`,
      code: `ZC${String(10000 + i).padStart(6, '0')}`,
      occupyName,
      manageUnit,
      reportOrNot: Math.floor(Math.random() * 2), // 0 或 1
      operator,
      entryClerk,
      createTime: formatDateTime(createTime),
      updateTime: formatDateTime(updateTime),
      status: statusMap[status],
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      selfDays,
      selfArea,
      assetsAmount,
      bookAmount,
      dateOfBookValue: formatDate(dateOfBookValue),
      purpose,
      profit: Math.floor(Math.random() * 2), // 0 或 1
      officeStandard: Math.floor(Math.random() * 3), // 0, 1, 2
      exceedance: Math.floor(Math.random() * 2), // 0 或 1
      remark,
    });
  }
  
  return data;
}

// 生成模拟数据
const selfData = generateSelfData(100);

export default [
  // 获取自用信息列表
  {
    url: `${baseUrl}/self/list`,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, limit = 10, name, code, type, manageUnit, status } = query;
      let filteredData = [...selfData];
      
      // 过滤数据
      if (name) {
        filteredData = filteredData.filter(item => item.name.includes(name));
      }
      if (code) {
        filteredData = filteredData.filter(item => item.code.includes(code));
      }
      if (type !== undefined && type !== '') {
        filteredData = filteredData.filter(item => item.type === Number(type));
      }
      if (manageUnit !== undefined && manageUnit !== '') {
        filteredData = filteredData.filter(item => item.manageUnit === manageUnits[Number(manageUnit)]);
      }
      if (status !== undefined && status !== '') {
        filteredData = filteredData.filter(item => item.status === Number(status));
      }
      
      // 分页
      return resultPageSuccess(Number(page), Number(limit), filteredData);
    },
  },
  
  // 新增自用信息
  {
    url: `${baseUrl}/self/add`,
    method: 'post',
    response: ({ body }) => {
      const newData = {
        ...body,
        id: selfData.length + 10001,
        createTime: formatDateTime(new Date()),
        updateTime: formatDateTime(new Date()),
      };
      selfData.push(newData);
      return resultSuccess(newData);
    },
  },
  
  // 编辑自用信息
  {
    url: `${baseUrl}/self/edit`,
    method: 'post',
    response: ({ body }) => {
      const index = selfData.findIndex(item => item.id === body.id);
      if (index !== -1) {
        selfData[index] = {
          ...selfData[index],
          ...body,
          updateTime: formatDateTime(new Date()),
        };
        return resultSuccess(selfData[index]);
      }
      return resultError('数据不存在');
    },
  },
  
  // 删除自用信息
  {
    url: `${baseUrl}/self/delete`,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = selfData.findIndex(item => item.id === Number(id));
      if (index !== -1) {
        selfData.splice(index, 1);
        return resultSuccess(null);
      }
      return resultError('数据不存在');
    },
  },
  
  // 获取自用信息详情
  {
    url: `${baseUrl}/self/detail`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const item = selfData.find(item => item.id === Number(id));
      if (item) {
        return resultSuccess(item);
      }
      return resultError('数据不存在');
    },
  },
  
  // 导出自用信息
  {
    url: `${baseUrl}/self/export`,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      const idList = ids.split(',').map(Number);
      const exportData = selfData.filter(item => idList.includes(item.id));
      
      // 这里应该返回文件流，mock中简单返回成功状态
      return resultSuccess({
        message: `成功导出${exportData.length}条数据`,
        data: exportData,
      });
    },
  },
  
  // 导出全部自用信息
  {
    url: `${baseUrl}/self/exportAll`,
    method: 'get',
    response: () => {
      return resultSuccess({
        message: `成功导出${selfData.length}条数据`,
        data: selfData,
      });
    },
  },
  
  // 导入自用信息
  {
    url: `${baseUrl}/self/import`,
    method: 'post',
    response: () => {
      // 模拟导入成功
      const importCount = Math.floor(Math.random() * 20) + 10;
      return resultSuccess({
        message: `成功导入${importCount}条数据`,
        count: importCount,
      });
    },
  },
  
  // 下载导入模板
  {
    url: `${baseUrl}/self/template`,
    method: 'get',
    response: () => {
      return resultSuccess({
        message: '模板下载成功',
        url: '/template/self-import-template.xlsx',
      });
    },
  },
] as MockMethod[]; 