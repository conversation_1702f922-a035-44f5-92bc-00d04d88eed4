import { MockMethod } from 'vite-plugin-mock';
import { resultError, resultPageSuccess, resultSuccess, baseUrl } from '../_util';

// 企业mock数据生成
const enterpriseNames = [
  '厦门市城市建设发展投资有限公司',
  '厦门市地热资源管理有限公司',
  '厦门兴地房屋征迁服务有限公司',
  '厦门地丰置业有限公司',
  '图智策划咨询（厦门）有限公司',
  '厦门市集众祥和物业管理有限公司',
  '厦门市人居乐业物业服务有限公司',
  '福州市建设投资集团有限责任公司',
  '福州市城市建设投资有限公司',
  '泉州市建设发展集团有限公司',
  '广州市城市建设投资集团有限公司',
  '深圳市投资控股有限公司',
  '深圳市建设集团有限公司',
  '杭州市城市建设投资集团有限公司',
  '宁波市建设集团股份有限公司',
  '厦门市轨道交通集团有限公司',
  '厦门市政工程有限公司',
  '厦门市绿化管理中心',
  '福建省建工集团有限责任公司',
  '广东省建筑工程集团有限公司',
];

const industries = [
  '房地产开发经营',
  '物业管理',
  '咨询服务',
  '建筑施工',
  '金融投资',
  '科技服务',
  '城市基础设施建设',
  '园林绿化',
  '交通运输',
  '环保服务',
  '能源管理',
  '工程监理',
];

const legalRepresentatives = [
  '张建国',
  '李明华',
  '王志强',
  '陈文博',
  '刘德华',
  '赵敏',
  '孙志远',
  '周晓明',
  '吴建设',
  '郑海峰',
  '朱永康',
  '胡正义',
  '林志强',
  '黄文斌',
  '蔡明亮',
  '许建华',
  '何志勇',
  '马建军',
  '邓建设',
  '曾志伟',
  '谢文华',
  '罗志强',
  '梁建国',
  '韩明',
];

const regions = [
  { province: '福建省', city: '厦门市', districts: ['思明区', '湖里区', '集美区', '海沧区', '同安区', '翔安区'] },
  { province: '福建省', city: '福州市', districts: ['鼓楼区', '台江区', '仓山区', '马尾区', '晋安区'] },
  { province: '福建省', city: '泉州市', districts: ['鲤城区', '丰泽区', '洛江区', '泉港区'] },
  { province: '广东省', city: '广州市', districts: ['天河区', '越秀区', '荔湾区', '海珠区', '白云区'] },
  { province: '广东省', city: '深圳市', districts: ['南山区', '福田区', '罗湖区', '宝安区', '龙岗区'] },
  { province: '广东省', city: '东莞市', districts: ['莞城区', '南城区', '东城区', '万江区'] },
  { province: '浙江省', city: '杭州市', districts: ['西湖区', '上城区', '拱墅区', '余杭区'] },
  { province: '浙江省', city: '宁波市', districts: ['海曙区', '江北区', '鄞州区', '镇海区'] },
];

const roads = ['建设路', '发展大道', '创新街', '科技园路', '工业大道', '商业街', '民主路', '和谐大道', '繁荣街', '幸福路'];

// 生成mock数据
const createEnterpriseData = () => {
  const data: any[] = [];

  for (let i = 1; i <= 86; i++) {
    const region = regions[i % regions.length];
    const district = region.districts[i % region.districts.length];
    const road = roads[i % roads.length];

    data.push({
      id: i,
      enterpriseName: enterpriseNames[i % enterpriseNames.length],
      enterpriseCode: i % 2, // 0-法人单位, 1-非法人单位
      locationProvince: region.province,
      locationCity: region.city,
      registration: `${region.city}${district}${road}${100 + i}号`,
      registeredCapital: (1000 + i * 50 + Math.random() * 500).toFixed(2),
      enterpriseType: i % 6, // 0-5 对应不同企业类型
      economicType: i % 7, // 0-6 对应不同经济类型
      legalRepresentative: legalRepresentatives[i % legalRepresentatives.length],
      scale: i % 4, // 0-大型, 1-中型, 2-小型, 3-微型
      orgCode: `913500${(10000000 + i).toString()}`,
      industry: industries[i % industries.length],
      createTime: new Date(2020 + (i % 4), i % 12, (i % 28) + 1).toISOString(),
      updateTime: new Date().toISOString(),
    });
  }

  return data;
};

// 数据存储
const enterpriseData = createEnterpriseData();

export default [
  // 查询企业列表
  {
    url: `${baseUrl}/enterprise/list`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const {
        pageNum = 1,
        pageSize = 10,
        enterpriseName,
        enterpriseCode,
        enterpriseType,
        economicType,
        location,
        legalRepresentative,
        industry,
        scale,
      } = query;

      let filtered = [...enterpriseData];

      // 搜索过滤
      if (enterpriseName) {
        filtered = filtered.filter((item) => item.enterpriseName.includes(enterpriseName));
      }

      if (enterpriseCode !== undefined && enterpriseCode !== '') {
        filtered = filtered.filter((item) => item.enterpriseCode === parseInt(enterpriseCode));
      }

      if (enterpriseType !== undefined && enterpriseType !== '') {
        filtered = filtered.filter((item) => item.enterpriseType === parseInt(enterpriseType));
      }

      if (economicType !== undefined && economicType !== '') {
        filtered = filtered.filter((item) => item.economicType === parseInt(economicType));
      }

      if (location && location.length > 0) {
        const locationArray = typeof location === 'string' ? location.split(',') : location;
        if (locationArray[0]) {
          filtered = filtered.filter((item) => item.locationProvince === locationArray[0]);
        }
        if (locationArray[1]) {
          filtered = filtered.filter((item) => item.locationCity === locationArray[1]);
        }
      }

      if (legalRepresentative) {
        filtered = filtered.filter((item) => item.legalRepresentative.includes(legalRepresentative));
      }

      if (industry) {
        filtered = filtered.filter((item) => item.industry.includes(industry));
      }

      if (scale !== undefined && scale !== '') {
        filtered = filtered.filter((item) => item.scale === parseInt(scale));
      }

      // 分页处理
      const pageNum_ = parseInt(pageNum);
      const pageSize_ = parseInt(pageSize);

      return resultPageSuccess(pageNum_, pageSize_, filtered);
    },
  },

  // 获取企业详情
  {
    url: `${baseUrl}/enterprise/detail`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const enterprise = enterpriseData.find((item) => item.id === parseInt(id));
      if (enterprise) {
        return resultSuccess(enterprise);
      }
      return resultError('企业信息不存在');
    },
  },

  // 新增企业
  {
    url: `${baseUrl}/enterprise/add`,
    timeout: 300,
    method: 'post',
    response: ({ body }) => {
      const newId = Math.max(...enterpriseData.map((item) => item.id)) + 1;
      const newEnterprise = {
        id: newId,
        ...body,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
      };

      enterpriseData.push(newEnterprise);

      return resultSuccess(newEnterprise, { message: '新增成功' });
    },
  },

  // 编辑企业
  {
    url: `${baseUrl}/enterprise/edit`,
    timeout: 300,
    method: 'put',
    response: ({ body }) => {
      const index = enterpriseData.findIndex((item) => item.id === body.id);
      if (index > -1) {
        enterpriseData[index] = {
          ...enterpriseData[index],
          ...body,
          updateTime: new Date().toISOString(),
        };

        return resultSuccess(enterpriseData[index], { message: '编辑成功' });
      }

      return resultError('编辑失败，数据不存在');
    },
  },

  // 删除企业
  {
    url: `${baseUrl}/enterprise/delete`,
    timeout: 300,
    method: 'get',
    response: ({ query }) => {
      console.log('delete', query);
      const { id } = query;
      const index = enterpriseData.findIndex((item) => item.id === parseInt(id));
      if (index > -1) {
        enterpriseData.splice(index, 1);
        return resultSuccess(null, { message: '删除成功' });
      }

      return resultError('删除失败，数据不存在');
    },
  },



  // 导出Excel
  {
    url: `${baseUrl}/enterprise/exportXls`,
    timeout: 300,
    method: 'get',
    response: ({ query: _query }) => {
      // 模拟导出功能
      return resultSuccess(null, { message: '导出成功' });
    },
  },

  // 导入Excel
  {
    url: `${baseUrl}/enterprise/importExcel`,
    timeout: 300,
    method: 'post',
    response: ({ body: _body }) => {
      // 模拟导入功能
      return resultSuccess(null, { message: '导入成功' });
    },
  },
] as MockMethod[]; 