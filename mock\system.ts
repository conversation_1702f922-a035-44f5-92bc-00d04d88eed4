import { MockMethod } from 'vite-plugin-mock';
import { baseUrl, resultPageSuccess, resultSuccess, resultError } from './_util';

// 定义数据类型接口
interface SystemFile {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface SystemData {
  id: number;
  code: string;
  name: string;
  groupName: number;
  companyName: number;
  manageUnit: number;
  reportOrNot: number;
  operator: string;
  auditDate: string;
  effectiveDate: string;
  expiringDate: string;
  systemFiles: SystemFile[];
  status: number;
  entryClerk: string;
  createTime: string;
  updateTime: string;
  remark: string;
}

// 生成模拟数据
function generateMockData(): SystemData[] {
  const mockData: SystemData[] = [];
  const statusOptions = [0, 1, 2, 4];
  const companyOptions = [0, 1, 2, 3, 4, 5, 6];
  const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const operators = ['经办人A', '经办人B', '经办人C'];
  const currentYear = new Date().getFullYear();

  for (let i = 0; i < 100; i++) {
    const auditDate = randomDate(new Date(currentYear - 1, 0, 1), new Date());
    const effectiveDate = new Date(auditDate);
    effectiveDate.setDate(auditDate.getDate() + Math.floor(Math.random() * 30) + 1);
    const expiringDate = new Date(effectiveDate);
    expiringDate.setFullYear(effectiveDate.getFullYear() + Math.floor(Math.random() * 5) + 1);

    const fileCount = Math.floor(Math.random() * 5) + 1;
    const systemFiles: SystemFile[] = [];
    for (let j = 0; j < fileCount; j++) {
      const fileTypes = ['docx', 'xlsx', 'pdf', 'jpg', 'png'];
      const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)];
      systemFiles.push({
        name: `附件${j + 1}.${fileType}`,
        url: `#`,
        size: Math.floor(Math.random() * 10000000),
        type: `application/${fileType}`,
      });
    }

    const companyName = companyOptions[Math.floor(Math.random() * companyOptions.length)];
    const manageUnit = companyOptions[Math.floor(Math.random() * companyOptions.length)];
    const creator = creators[Math.floor(Math.random() * creators.length)];
    const operator = operators[Math.floor(Math.random() * operators.length)];

    const createDate = new Date(
      currentYear,
      Math.floor(Math.random() * 12),
      Math.floor(Math.random() * 28) + 1,
      Math.floor(Math.random() * 24),
      Math.floor(Math.random() * 60),
      Math.floor(Math.random() * 60)
    );
    const updateDate = new Date(createDate);
    updateDate.setDate(updateDate.getDate() + Math.floor(Math.random() * 30));

    mockData.push({
      id: 10000 + i,
      code: `ZD${currentYear}${String(i + 1).padStart(4, '0')}`,
      name: `企业管理制度${i + 1}`,
      groupName: 0,
      companyName: companyName,
      manageUnit: manageUnit,
      reportOrNot: Math.round(Math.random()),
      operator: operator,
      auditDate: formatDate(auditDate),
      effectiveDate: formatDate(effectiveDate),
      expiringDate: formatDate(expiringDate),
      systemFiles: systemFiles,
      status: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      entryClerk: creator,
      createTime: formatDateTime(createDate),
      updateTime: formatDateTime(updateDate),
      remark: Math.random() > 0.5 ? `这是制度${i + 1}的备注信息` : '',
    });
  }

  return mockData;
}

function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

function formatDateTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

const mockData = generateMockData();

export default [
  {
    url: `${baseUrl}/system/list`,
    method: 'get',
    response: ({ query }) => {
      const { page = 1, limit = 10, column = 'createTime', order = 'desc', code, name, companyName, status } = query;
      
      let filteredData = [...mockData];
      
      // 处理搜索条件
      if (code) {
        filteredData = filteredData.filter((item) => item.code.includes(code));
      }
      if (name) {
        filteredData = filteredData.filter((item) => item.name.includes(name));
      }
      if (companyName !== undefined && companyName !== '') {
        filteredData = filteredData.filter((item) => item.companyName === parseInt(companyName));
      }
      if (status !== undefined && status !== '') {
        filteredData = filteredData.filter((item) => item.status === parseInt(status));
      }
      
      // 排序
      filteredData.sort((a, b) => {
        const aValue = a[column as keyof SystemData];
        const bValue = b[column as keyof SystemData];
        if (order === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
      
      // 使用统一的分页工具函数
      return resultPageSuccess(Number(page), Number(limit), filteredData);
    },
  },

  {
    url: `${baseUrl}/system/add`,
    method: 'post',
    response: ({ body }) => {
      const newId = Math.max(...mockData.map((item) => item.id)) + 1;
      const currentYear = new Date().getFullYear();
      const newCode = `ZD${currentYear}${String(newId).padStart(4, '0')}`;
      
      const newItem: SystemData = {
        ...body,
        id: newId,
        code: newCode,
        createTime: formatDateTime(new Date()),
        updateTime: formatDateTime(new Date()),
      } as SystemData;
      
      mockData.push(newItem);
      
      return resultSuccess(newItem, { message: '新增成功' });
    },
  },

  {
    url: `${baseUrl}/system/edit`,
    method: 'post',
    response: ({ body }) => {
      const index = mockData.findIndex((item) => item.id === body.id);
      if (index !== -1) {
        mockData[index] = {
          ...mockData[index],
          ...body,
          updateTime: formatDateTime(new Date()),
        };
        
        return resultSuccess(mockData[index], { message: '更新成功' });
      } else {
        return resultError('数据不存在');
      }
    },
  },

  {
    url: `${baseUrl}/system/delete`,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      const index = mockData.findIndex((item) => item.id === parseInt(id));
      if (index !== -1) {
        mockData.splice(index, 1);
        return resultSuccess(null, { message: '删除成功' });
      } else {
        return resultError('数据不存在');
      }
    },
  },

  {
    url: `${baseUrl}/system/detail`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      const item = mockData.find((item) => item.id === parseInt(id));
      
      if (item) {
        return resultSuccess(item, { message: '获取成功' });
      } else {
        return resultError('数据不存在');
      }
    },
  },

  {
    url: `${baseUrl}/system/importExcel`,
    method: 'post',
    response: () => {
      return resultSuccess(15, { message: '导入成功' });
    },
  },

  {
    url: `${baseUrl}/system/exportXls`,
    method: 'get',
    response: ({ query }) => {
      const { ids } = query;
      const idArray = ids.split(',').map((id) => parseInt(id));
      const exportData = mockData.filter((item) => idArray.includes(item.id));
      
      return resultSuccess(exportData, { message: '导出成功' });
    },
  },

  {
    url: `${baseUrl}/system/exportAll`,
    method: 'get',
    response: () => {
      return resultSuccess(mockData, { message: '导出成功' });
    },
  },

  {
    url: `${baseUrl}/system/downloadTemplate`,
    method: 'get',
    response: () => {
      return resultSuccess('template.xlsx', { message: '下载成功' });
    },
  },

  {
    url: `${baseUrl}/system/upload`,
    method: 'post',
    response: () => {
      return resultSuccess({
        name: 'uploaded_file.pdf',
        url: 'https://example.com/files/uploaded_file.pdf',
        size: 1024000,
      }, { message: '上传成功' });
    },
  },
] as MockMethod[];
