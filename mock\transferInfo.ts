import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 模拟数据库
const mockDb = {
  transferInfoList: (() => {
    const result = [];
    for (let i = 0; i < 50; i++) {
      result.push({
        id: i + 1,
        code: `ZRXY${String(i + 1).padStart(3, '0')}${new Date().getFullYear()}0${Math.floor(Math.random() * 9) + 1}0000${i + 1}`,
        name: `${Random.city()}${['商业楼宇', '办公大厦', '商铺', '工业厂房', '商业综合体', '土地使用权', '设备资产'][Math.floor(Math.random() * 7)]}资产包${i + 1}`,
        type: Random.integer(0, 5),
        assetsType: Random.integer(0, 4),
        manageUnit: Random.integer(0, 6),
        listingPrice: Random.float(1000, 100000, 2, 2),
        sellName: `${Random.city()}${['国有', '市政', '城建', '华夏', '中铁', '中建', '恒大', '万科'][Math.floor(Math.random() * 8)]}${['资产管理', '房地产', '建设集团', '投资', '控股', '置业'][Math.floor(Math.random() * 6)]}有限公司`,
        status: [0, 1, 2, 4][Math.floor(Math.random() * 4)],
        dealStatus: Random.integer(0, 1),
        transferee: Random.integer(0, 1) === 1 ? `${Random.city()}${['宏达', '恒信', '鑫源', '盛世', '汇通', '金鑫', '华润', '宝龙'][Math.floor(Math.random() * 8)]}${['商贸', '科技', '物流', '餐饮', '服饰', '电子', '文化传媒'][Math.floor(Math.random() * 7)]}有限公司` : '',
        dealPrice: Random.integer(0, 1) === 1 ? Random.float(900, 110000, 2, 2) : null,
        listingStartDate: Random.date('yyyy-MM-dd'),
        listingEndDate: Random.date('yyyy-MM-dd'),
        targetName: `${['中山路临街商铺', '城南停车场地块', '工业园区厂房', '商业中心写字楼', '住宅小区商铺'][Math.floor(Math.random() * 5)]}${i + 1}`,
        assetName: `${['商业楼', '办公楼', '商铺', '厂房', '土地'][Math.floor(Math.random() * 5)]}${i + 1}号`,
        assetCode: `ZC${String(2000 + i).padStart(6, '0')}`,
        operator: Random.cname(),
        entryClerk: Random.cname(),
        createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
        contentDescription: `这是一个位于${Random.city()}的优质${['商业', '办公', '工业', '住宅'][Math.floor(Math.random() * 4)]}资产包，包含多个${['临街商铺', '办公空间', '工业厂房', '住宅单元'][Math.floor(Math.random() * 4)]}。`,
      });
    }
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/transferInfo/list`,
    method: 'get',
    response: ({ query }) => {
      const { 
        pageNo = 1, 
        pageSize = 10, 
        name, 
        code, 
        status, 
        dealStatus,
        manageUnit,
        sellName,
        assetName,
        assetCode,
        assetsType,
        type,
        transferee,
        operator,
        entryClerk,
        targetName,
        priceRange,
        listingDateRange,
        createTimeRange
      } = query;
      
      let list = [...mockDb.transferInfoList];
      
      // 条件筛选
      if (name) {
        list = list.filter(item => item.name.includes(name));
      }
      
      if (code) {
        list = list.filter(item => item.code.includes(code));
      }
      
      if (status !== undefined && status !== null && status !== '') {
        list = list.filter(item => item.status === parseInt(status));
      }

      if (dealStatus !== undefined && dealStatus !== null && dealStatus !== '') {
        list = list.filter(item => item.dealStatus === parseInt(dealStatus));
      }

      if (manageUnit !== undefined && manageUnit !== null && manageUnit !== '') {
        list = list.filter(item => item.manageUnit === parseInt(manageUnit));
      }

      if (sellName) {
        list = list.filter(item => item.sellName.includes(sellName));
      }

      if (assetName) {
        list = list.filter(item => item.assetName.includes(assetName));
      }

      if (assetCode) {
        list = list.filter(item => item.assetCode.includes(assetCode));
      }

      if (assetsType !== undefined && assetsType !== null && assetsType !== '') {
        list = list.filter(item => item.assetsType === parseInt(assetsType));
      }

      if (type !== undefined && type !== null && type !== '') {
        list = list.filter(item => item.type === parseInt(type));
      }

      if (transferee) {
        list = list.filter(item => item.transferee && item.transferee.includes(transferee));
      }

      if (operator) {
        list = list.filter(item => item.operator.includes(operator));
      }

      if (entryClerk) {
        list = list.filter(item => item.entryClerk.includes(entryClerk));
      }

      if (targetName) {
        list = list.filter(item => item.targetName.includes(targetName));
      }

      // 价格范围筛选
      if (priceRange && Array.isArray(priceRange) && priceRange.length === 2) {
        const [minPrice, maxPrice] = priceRange;
        if (minPrice !== null && minPrice !== undefined) {
          list = list.filter(item => item.listingPrice >= minPrice);
        }
        if (maxPrice !== null && maxPrice !== undefined) {
          list = list.filter(item => item.listingPrice <= maxPrice);
        }
      }

      // 挂牌时间范围筛选
      if (listingDateRange && Array.isArray(listingDateRange) && listingDateRange.length === 2) {
        const [startDate, endDate] = listingDateRange;
        if (startDate) {
          list = list.filter(item => item.listingStartDate >= startDate);
        }
        if (endDate) {
          list = list.filter(item => item.listingEndDate <= endDate);
        }
      }

      // 录入时间范围筛选
      if (createTimeRange && Array.isArray(createTimeRange) && createTimeRange.length === 2) {
        const [startDate, endDate] = createTimeRange;
        if (startDate) {
          list = list.filter(item => item.createTime >= startDate);
        }
        if (endDate) {
          list = list.filter(item => item.createTime <= endDate + ' 23:59:59');
        }
      }
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 获取详情
  {
    url: `${baseUrl}/transferInfo/detail`,
    method: 'get',
    response: ({ query }) => {
      const { id } = query;
      
      // 查找对应ID的记录
      const item = mockDb.transferInfoList.find(item => item.id === parseInt(id));
      
      if (!item) {
        return resultError('未找到对应记录');
      }
      
      // 补充详情信息
      const detail = {
        ...item,
        assetsLocation: ['fujian', 'xiamen', 'siming'],
        source: Random.integer(0, 2),
        reportOrNot: Random.integer(0, 1),
        remark: '这是一个测试资产包',
        listingOrganization: item.type === 1 ? '厦门产权交易中心' : '',
        listingLocation: item.type === 1 ? ['fujian', 'xiamen'] : [],
        sellType: Random.integer(0, 1),
        jointSell: Random.integer(0, 1),
        sellLocation: ['fujian', 'xiamen', 'huli'],
        registration: '厦门市思明区会展路123号',
        registeredCapital: Random.float(1000, 100000, 2, 2),
        enterpriseType: Random.integer(0, 5),
        economicType: Random.integer(0, 6),
        legalRepresentative: Random.cname(),
        scale: Random.integer(0, 3),
        orgCode: '91350200' + Random.string('number', 10),
        decisionMaking: Random.integer(0, 3),
        evaluateOrg: '厦门某某资产评估有限公司',
        approvalOrg: '厦门市国资委',
        approvalDate: Random.date('yyyy-MM-dd'),
        evaluateDate: Random.date('yyyy-MM-dd'),
        evaluateReport: 'PG' + Random.string('number', 8),
        evaluateDateOrg: '厦门某某会计师事务所',
        lawFirm: '厦门某某律师事务所',
        targetPrice: Random.float(5000, 50000, 2, 2),
        sellTargetPrice: Random.float(5000, 50000, 2, 2),
        bookAmount: Random.float(4000, 45000, 2, 2),
        bookNetAmount: Random.float(3000, 40000, 2, 2),
        sellFiles: [
          {
            uid: 'file-1',
            name: '转让方资料.pdf',
            status: 'done',
            url: 'https://example.com/files/transferor.pdf'
          }
        ],
        disclosureFiles: [
          {
            uid: 'file-2',
            name: '披露信息.pdf',
            status: 'done',
            url: 'https://example.com/files/disclosure.pdf'
          }
        ],
        assetList: [
          {
            id: 1,
            assetType: 1,
            assetsCode: ['ZC005'],
            targetName: '中山路临街商铺',
            listingPrice: 5000,
            evaluatePrice: 4800,
            sellArea: 300,
            bookNetAmount: 4500
          },
          {
            id: 2,
            assetType: 0,
            assetsCode: ['ZC006'],
            targetName: '城南停车场地块',
            listingPrice: 25000,
            evaluatePrice: 24000,
            sellArea: 25000,
            bookNetAmount: 23000
          }
        ],
        dealList: [
          {
            id: 1,
            targetName: '中山路临街商铺',
            dealStatus: 1,
            transferee: '厦门市某某商贸有限公司',
            dealPrice: 5200,
            dealDate: '2023-08-15',
            dealBeginDate: '2023-09-01',
            dealEndDate: '2026-08-31'
          },
          {
            id: 2,
            targetName: '城南停车场地块',
            dealStatus: 0,
            transferee: '',
            dealPrice: null,
            dealDate: '',
            dealBeginDate: '',
            dealEndDate: ''
          }
        ]
      };
      
      return resultSuccess(detail);
    },
  },
  
  // 保存
  {
    url: `${baseUrl}/transferInfo/save`,
    method: 'post',
    response: ({ body }) => {
      const id = mockDb.transferInfoList.length + 1;
      const code = `ZRXY${String(id).padStart(3, '0')}${new Date().getFullYear()}0${Math.floor(Math.random() * 9) + 1}0000${id}`;
      
      const newItem = {
        ...body,
        id,
        code,
        createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      };
      
      mockDb.transferInfoList.push(newItem);
      
      return resultSuccess({
        id,
        code,
      });
    },
  },
  
  // 更新
  {
    url: `${baseUrl}/transferInfo/update`,
    method: 'post',
    response: ({ body }) => {
      const { id } = body;
      
      const index = mockDb.transferInfoList.findIndex(item => item.id === id);
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.transferInfoList[index] = {
        ...mockDb.transferInfoList[index],
        ...body,
      };
      
      return resultSuccess(true);
    },
  },
  
  // 删除
  {
    url: `${baseUrl}/transferInfo/delete`,
    method: 'delete',
    response: ({ query }) => {
      const { id } = query;
      
      const index = mockDb.transferInfoList.findIndex(item => item.id === parseInt(id));
      
      if (index === -1) {
        return resultError('未找到对应记录');
      }
      
      mockDb.transferInfoList.splice(index, 1);
      
      return resultSuccess(true);
    },
  },

  // 导出选中数据
  {
    url: `${baseUrl}/transferInfo/export`,
    method: 'post',
    response: ({ body }) => {
      const { ids } = body;

      // 模拟导出文件
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = 'data:application/vnd.ms-excel;base64,';
        link.download = `转让信息_${new Date().getTime()}.xlsx`;
        link.click();
      }, 1000);

      return resultSuccess({
        message: '导出成功',
        count: ids.length
      });
    },
  },

  // 导出全部数据
  {
    url: `${baseUrl}/transferInfo/exportAll`,
    method: 'post',
    response: () => {
      // 模拟导出文件
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = 'data:application/vnd.ms-excel;base64,';
        link.download = `转让信息_全部_${new Date().getTime()}.xlsx`;
        link.click();
      }, 1000);

      return resultSuccess({
        message: '导出成功',
        count: mockDb.transferInfoList.length
      });
    },
  },

  // 导入数据
  {
    url: `${baseUrl}/transferInfo/import`,
    method: 'post',
    response: () => {
      // 模拟导入结果
      const successCount = Random.integer(5, 20);
      const failCount = Random.integer(0, 3);

      return resultSuccess({
        successCount,
        failCount,
        errors: failCount > 0 ? [
          '第2行：资产包名称不能为空',
          '第5行：转让方式格式错误',
          '第8行：挂牌价格必须为数字'
        ].slice(0, failCount) : []
      });
    },
  },

  // 下载导入模板
  {
    url: `${baseUrl}/transferInfo/downloadTemplate`,
    method: 'get',
    response: () => {
      // 模拟下载模板文件
      setTimeout(() => {
        const link = document.createElement('a');
        link.href = 'data:application/vnd.ms-excel;base64,';
        link.download = '转让信息导入模板.xlsx';
        link.click();
      }, 500);

      return resultSuccess({
        message: '模板下载成功'
      });
    },
  },

  // 获取资产选项
  {
    url: `${baseUrl}/assets/options`,
    method: 'get',
    response: ({ query }) => {
      const { keyword, assetType } = query;

      const assetOptions = [];
      for (let i = 0; i < 20; i++) {
        const assetName = `${['商业楼', '办公楼', '商铺', '厂房', '土地'][Math.floor(Math.random() * 5)]}${i + 1}号`;
        const assetCode = `ZC${String(1000 + i).padStart(6, '0')}`;

        if (!keyword || assetName.includes(keyword) || assetCode.includes(keyword)) {
          assetOptions.push({
            value: assetCode,
            label: `${assetName}（${assetCode}）`,
            assetType: Math.floor(Math.random() * 5)
          });
        }
      }

      let filteredOptions = assetOptions;
      if (assetType !== undefined && assetType !== null && assetType !== '') {
        filteredOptions = assetOptions.filter(item => item.assetType === parseInt(assetType));
      }

      return resultSuccess(filteredOptions.slice(0, 10));
    },
  },

  // 获取转让方选项
  {
    url: `${baseUrl}/transferor/options`,
    method: 'get',
    response: ({ query }) => {
      const { keyword } = query;

      const transferorOptions = [
        '厦门市城市建设发展投资有限公司',
        '厦门市地热资源管理有限公司',
        '厦门兴地房屋征迁服务有限公司',
        '厦门地丰置业有限公司',
        '图智策划咨询（厦门）有限公司',
        '厦门市集众祥和物业管理有限公司',
        '厦门市人居乐业物业服务有限公司',
        '厦门国贸集团股份有限公司',
        '厦门建发集团有限公司',
        '厦门象屿集团有限公司'
      ];

      let filteredOptions = transferorOptions;
      if (keyword) {
        filteredOptions = transferorOptions.filter(item => item.includes(keyword));
      }

      return resultSuccess(
        filteredOptions.map(item => ({
          value: item,
          label: item
        }))
      );
    },
  },
] as MockMethod[];
