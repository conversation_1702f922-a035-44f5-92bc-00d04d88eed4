import { MockMethod } from 'vite-plugin-mock';
import { resultSuccess, resultError, baseUrl } from './_util';
import { Random } from 'mockjs';

// 业务类型映射
const businessTypeMap = {
  'land': '土地',
  'house': '房屋',
  'advert': '广告位',
  'equipment': '设备',
  'other': '其他资产',
  'rent': '租赁',
  'rentAbort': '租赁中止',
  'rentAd': '招租公告',
  'rentDetail': '租金明细',
  'transfer': '转让',
  'vacant': '空置/闲置',
  'occupy': '占用',
  'lend': '借出',
  'self': '自用',
  'systemInfo': '制度信息',
  'rentOverview': '租金一览',
};

const businessTypes = Object.keys(businessTypeMap);
const fileTypes = ['FILE', 'DB'];
const feedbackStatusList = ['已处理', '处理中', '待处理'];
const feedbackContentList = ['数据已成功接收', '数据格式有误', '数据包校验失败', '处理成功'];

// 生成时间字符串
function formatDateTime(date: Date): string {
  return date.toISOString().slice(0, 19).replace('T', ' ');
}

// 模拟数据库
const mockDb = {
  uploadRecordList: (() => {
    const result = [];
    for (let i = 0; i < 56; i++) {
      // 生成随机上传时间（1-30天前）
      const uploadDate = new Date();
      uploadDate.setDate(uploadDate.getDate() - Math.floor(Math.random() * 30) - 1);
      
      // 随机上传结果
      const uploadResult = Math.random() > 0.3 ? 1 : 0;
      
      // 生成数据包名称
      const packageNumber = String(100 + i).padStart(4, '0');
      const randomDate = new Date();
      randomDate.setDate(randomDate.getDate() + Math.floor(Math.random() * 365));
      const dateStr = randomDate.toISOString().slice(0, 10).replace(/-/g, '');
      
      let receiveTime = null;
      let pushTime = null;
      let fetchTime = null;
      let feedbackTime = null;
      let feedbackStatus = null;
      let feedbackContent = null;
      
      if (uploadResult === 1) {
        // 生成接收时间（上传后0-10分钟）
        const receiveDate = new Date(uploadDate);
        receiveDate.setMinutes(receiveDate.getMinutes() + Math.floor(Math.random() * 10));
        receiveTime = formatDateTime(receiveDate);
        
        // 生成推送时间（接收后0-30分钟）
        const pushDate = new Date(receiveDate);
        pushDate.setMinutes(pushDate.getMinutes() + Math.floor(Math.random() * 30));
        pushTime = formatDateTime(pushDate);
        
        // 生成抓取时间（推送后0-60分钟）
        const fetchDate = new Date(pushDate);
        fetchDate.setMinutes(fetchDate.getMinutes() + Math.floor(Math.random() * 60));
        fetchTime = formatDateTime(fetchDate);
        
        // 生成反馈时间
        const feedbackDate = new Date(fetchDate);
        feedbackDate.setMinutes(feedbackDate.getMinutes() + Math.floor(Math.random() * 30));
        feedbackTime = formatDateTime(feedbackDate);
        
        feedbackStatus = feedbackStatusList[Math.floor(Math.random() * feedbackStatusList.length)];
        feedbackContent = feedbackContentList[Math.floor(Math.random() * feedbackContentList.length)];
      }
      
      const businessType = businessTypes[Math.floor(Math.random() * businessTypes.length)];
      
      result.push({
        id: i + 1,
        packageName: `00${packageNumber}_${Math.floor(Math.random() * 9000) + 1000}_${dateStr}.zip`,
        businessType: businessTypeMap[businessType],
        businessTypeCode: businessType,
        fileCount: Math.floor(Math.random() * 20) + 1,
        uploadTime: formatDateTime(uploadDate),
        uploadResult: uploadResult,
        receiveTime: receiveTime,
        pushTime: pushTime,
        fetchTime: fetchTime,
        feedbackTime: feedbackTime,
        feedbackStatus: feedbackStatus,
        feedbackContent: feedbackContent,
        fileType: fileTypes[Math.floor(Math.random() * fileTypes.length)],
      });
    }
    return result;
  })(),
};

export default [
  // 获取列表
  {
    url: `${baseUrl}/uploadRecord/list`,
    method: 'get',
    response: ({ query }) => {
      const { 
        pageNo = 1, 
        pageSize = 10, 
        businessTypes, 
        uploadTimeRange, 
        uploadResult 
      } = query;
      
      let list = [...mockDb.uploadRecordList];
      
      // 条件筛选
      if (businessTypes && businessTypes.length > 0) {
        const businessTypeArray = Array.isArray(businessTypes) ? businessTypes : businessTypes.split(',');
        list = list.filter(item => businessTypeArray.includes(item.businessTypeCode));
      }
      
      if (uploadTimeRange && uploadTimeRange.length === 2) {
        const [startTime, endTime] = uploadTimeRange;
        list = list.filter(item => {
          const uploadTime = item.uploadTime.split(' ')[0];
          return uploadTime >= startTime && uploadTime <= endTime;
        });
      }
      
      if (uploadResult !== undefined && uploadResult !== null && uploadResult !== '') {
        list = list.filter(item => item.uploadResult === parseInt(uploadResult));
      }
      
      // 分页
      const pageList = list.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      
      return resultSuccess({
        records: pageList,
        total: list.length,
        pageNo: parseInt(pageNo),
        pageSize: parseInt(pageSize),
      });
    },
  },
  
  // 重新上传单个记录
  {
    url: `${baseUrl}/uploadRecord/reupload`,
    method: 'post',
    response: ({ body }) => {
      const { id } = body;
      
      // 查找对应ID的记录
      const item = mockDb.uploadRecordList.find(item => item.id === parseInt(id));
      
      if (!item) {
        return resultError('未找到对应记录');
      }
      
      // 模拟重新上传成功，更新记录
      const now = new Date();
      item.uploadResult = 1;
      item.receiveTime = formatDateTime(now);
      
      const pushDate = new Date(now);
      pushDate.setMinutes(pushDate.getMinutes() + 5);
      item.pushTime = formatDateTime(pushDate);
      
      const fetchDate = new Date(pushDate);
      fetchDate.setMinutes(fetchDate.getMinutes() + 10);
      item.fetchTime = formatDateTime(fetchDate);
      
      const feedbackDate = new Date(fetchDate);
      feedbackDate.setMinutes(feedbackDate.getMinutes() + Math.floor(Math.random() * 30));
      item.feedbackTime = formatDateTime(feedbackDate);
      
      item.feedbackStatus = '处理中';
      item.feedbackContent = '数据已成功接收';
      
      return resultSuccess('重新上传成功');
    },
  },
  
  // 批量重新上传
  {
    url: `${baseUrl}/uploadRecord/batchReupload`,
    method: 'post',
    response: ({ body }) => {
      const { ids } = body;
      
      if (!ids || !Array.isArray(ids)) {
        return resultError('参数错误');
      }
      
      // 批量更新记录
      ids.forEach(id => {
        const item = mockDb.uploadRecordList.find(item => item.id === parseInt(id));
        if (item) {
          const now = new Date();
          item.uploadResult = 1;
          item.receiveTime = formatDateTime(now);
          
          const pushDate = new Date(now);
          pushDate.setMinutes(pushDate.getMinutes() + 5);
          item.pushTime = formatDateTime(pushDate);
          
          const fetchDate = new Date(pushDate);
          fetchDate.setMinutes(fetchDate.getMinutes() + 10);
          item.fetchTime = formatDateTime(fetchDate);
          
          const feedbackDate = new Date(fetchDate);
          feedbackDate.setMinutes(feedbackDate.getMinutes() + Math.floor(Math.random() * 30));
          item.feedbackTime = formatDateTime(feedbackDate);
          
          item.feedbackStatus = '处理中';
          item.feedbackContent = '数据已成功接收';
        }
      });
      
      return resultSuccess('批量重新上传成功');
    },
  },
] as MockMethod[];
