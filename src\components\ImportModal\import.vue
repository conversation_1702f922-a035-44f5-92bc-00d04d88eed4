<template>
  <div class="p-4 data-import">
    <div style="margin-bottom: 20px;">
      <a-alert message="请先下载导入模板，按照模板格式填写后上传" type="info" :closable="false" show-icon />
    </div>
    <div style="margin-bottom: 30px;">
      <a-button type="primary" :loading="exportLoading" @click="downloadTemplate">
        <template #icon>
          <DownloadOutlined />
        </template>
        下载导入模板
      </a-button>
    </div>

    <a-upload
      v-model:file-list="fileList"
      name="file"
      :multiple="false"
      :maxCount="1"
      :before-upload="customRequest"
      :show-upload-list="{ showPreviewIcon: false, showDownloadIcon: false, showRemoveIcon: true }"
      accept=".xls,.xlsx"
      @remove="removeFile"
    >
      <a-button>
        <template #icon>
          <UploadOutlined />
        </template>
        选择文件
      </a-button>
      <template #itemRender="{ file }">
        <span>{{ file.name }}</span>
      </template>
    </a-upload>

    <div style="margin-top: 10px; color: #909399; font-size: 12px;">
      只能上传xlsx/xls文件，且不超过10MB
    </div>


    <!-- <a-row :gutter="[16, 16]">
      <a-col :span="24">
        <a-card>
          <div class="import-option">
            <div class="icon-container">
              <cloud-upload-outlined class="large-icon" />
            </div>
            <div class="option-content">
              <h3>上传填好的信息表</h3>
              <p>文件后缀名必须为xls、xlsx（即Excel格式），文件大小不得大于10M，最多支持导入3000条数据</p>
              <a-upload
                :maxCount="1"
                name="file"
                v-model:file-list="fileList"
                :customRequest="customRequest"
                :show-upload-list="{ showPreviewIcon: false, showDownloadIcon: false, showRemoveIcon: true }"
                accept=".xls,.xlsx"
                @remove="removeFile"
              >
                <a-button type="primary"> 上传文件 </a-button>
              </a-upload>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
    <a-alert message="特别提示" description="导入过程中请勿关闭页面，保持页面连接" type="warning" show-icon class="warning-alert" /> -->
  </div>
  <!-- <div class="p-4 data-statistics" v-if="step === 2">
    <a-card class="statistics-card">
      <div class="import-option">
        <div class="icon-container">
          <bulb-outlined class="icon" />
        </div>
        <div class="option-content">
          <div class="statistics-item">
            <span>正常数量条数: </span>
            <span class="value">{{ uploadResultData.sucCount }}条</span>
          </div>
          <div class="statistics-item">
            <span>异常数量条数: </span>
            <span class="value">{{ uploadResultData.errCount }}条</span>
          </div>
        </div>
      </div>
    </a-card>

    <a-card class="info-card" v-if="uploadResultData.errId">
      <div class="import-option">
        <div class="icon-container">
          <cloud-download-outlined class="icon" />
        </div>
        <div class="option-content">
          <h3>异常数据信息</h3>
          <p>已检验并标记出异常数据，可下载文件进行编辑后重新上传，或点一下步上传正常数据（异常数据将被过滤）</p>
          <a-button type="link" class="link" @click="downloadErrFile">下载文件</a-button>
        </div>
      </div>
    </a-card>
  </div> -->
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { CloudDownloadOutlined, CloudUploadOutlined, BulbOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  // import { exportErrData } from '/@/api/common/api';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { useGlobSetting } from '/@/hooks/setting';

  const props = defineProps<{
    step: number;
    exportTemplateMethod: string;
    exportTemplateName: string;
    exportTemplateUrl: string;
    importUrl: string;
    importMethod: string;
  }>();

  const glob = useGlobSetting();
  const exportLoading = ref(false);
  const uploadResultData = ref({});
  const fileList = ref([]);
  const { handleExportXls, handleImportXls } = useMethods();
  const downloadTemplate = () => {
    // Implement download logic here
    exportLoading.value = true;
    handleExportXls(props.exportTemplateName || '模板文件', props.exportTemplateUrl, { downloadType: 'template' }, props.exportTemplateMethod)
      .then(() => {
        console.log('导出成功');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  };

  const beforeUpload = (file) => {
    console.log('beforeUpload', file);
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
    if (!isExcel) {
      message.error('请上传正确格式');
      return false;
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('上传失败，文件过大');
      return false;
    }
    console.log('beforeUpload', file);
    return true;
  };

  let fileCurrent = null;

  function customRequest(file) {
    console.log('customRequest', file);
    const validate = beforeUpload(file);
    console.log('validate', validate);
    if (validate) {
      fileCurrent = file;
      fileList.value.forEach((item) => (item.status = 'done'));
    } else {
      fileList.value = [];
      fileCurrent = null;
    }
    console.log('fileCurrent', fileCurrent);
  }

  function removeFile() {
    fileCurrent = null;
    fileList.value = [];
  }

  function importData() {
    console.log('importData', fileCurrent);
    if (!fileCurrent) return message.warning('请上传文件');
    return new Promise((resolve, reject) => {
      handleImportXls({ file: fileCurrent }, glob.domainUrl + props.importUrl, function (res) {
        if (res.code === 200) {
          uploadResultData.value = res.result || {};
          resolve(res.result);
        } else {
          reject();
        }
      }).catch(() => {
        reject();
      });
    });
  }

  // function downloadErrFile() {
  //   handleExportXls('导入错误数据', exportErrData, { errId: uploadResultData.value.errId }, 'get')
  //     .then(() => {
  //       console.log('导出成功');
  //     })
  //     .finally(() => {
  //       exportLoading.value = false;
  //     });
  // }

  function reImport() {
    fileList.value = [];
    fileCurrent = null;
  }

  defineExpose({ importData, reImport });
</script>

<style lang="less">
  .import-option {
    display: flex;
    align-items: flex-start;

    .icon-container {
      margin-right: 16px;
    }

    .large-icon {
      font-size: 48px;
      color: #1890ff;
    }

    .option-content {
      flex: 1;

      h3 {
        margin-bottom: 8px;
      }

      p {
        margin-bottom: 16px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .warning-alert {
    margin-top: 16px;
  }

  .data-statistics {
    max-width: 800px;
    margin: 0 auto;

    .statistics-card,
    .info-card {
      margin-bottom: 16px;
      // background-color: #f5f5f5;
    }

    .icon {
      font-size: 48px;
      color: #1890ff;
    }

    .statistics-item {
      font-size: 16px;
      margin-bottom: 8px;

      .value {
        font-weight: bold;
        margin-left: 8px;
      }
    }

    h3 {
      margin-bottom: 8px;
    }

    p {
      margin-bottom: 16px;
      color: rgba(0, 0, 0, 0.65);
    }

    .link {
      padding-left: 0;
      padding-right: 0;
    }
  }
</style>
