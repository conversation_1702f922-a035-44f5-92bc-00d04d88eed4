<template>
  <a-button preIcon="ant-design:import-outlined" @click="handleImport" v-if="noBtn">导入数据</a-button>
  <div @click="handleImport" v-else>{{ title || '导入' }}</div>
</template>

<script lang="ts">
  import { defineComponent, h, ref, unref } from 'vue';
  import { useModal } from '/@/utils/use/use-modal';
  import ImportComponent from './import.vue';

  export default defineComponent({
    props: {
      exportTemplateUrl: String,
      exportTemplateName: String,
      exportTemplateMethod: String,
      importUrl: String,
      importMethod: String,
      initTitle: String,
      isBtn: {
        type: Boolean,
        default: true,
      },
    },
    setup(props, { attrs, slots, emit }) {
      const modalRef = ref();
      const step = ref(1);
      const { createModal } = useModal();
      function handleImport() {
        step.value = 1;
        const modal = createModal({
          title: '数据导入',
          content: () =>
            h(ImportComponent, {
              ref: modalRef,
              exportTemplateUrl: props.exportTemplateUrl,
              exportTemplateName: props.exportTemplateName,
              exportTemplateMethod: props.exportTemplateMethod || 'get',
              importUrl: props.importUrl,
              importMethod: props.importMethod || 'post',
              step: step.value,
            }),
          width: 700,
          bodyStyle: { maxHeight: 'calc(100vh - 200px)', overflow: 'auto' },
          okText: '导入',
          async onOk() {
            if (step.value === 1) {
              unref(modalRef)
                .importData()
                .then((res) => {
                  if (res) {
                    console.log(res, 'res');
                    if (res === '导入成功!') {
                      modal.hide();
                      emit('success', res);
                    } else {
                      modal.update({
                        okText: '重新导入',
                      });
                    }
                    // step.value = 2;
                    // if (res.sucCount) emit('success', res?.billList || []);
                    // modal.update({
                    //   okText: '重新导入',
                    // });
                  }
                });
            } else {
              // step.value = 1;
              modal.update({
                okText: '导入',
              });
              unref(modalRef).reImport();
            }
          },
        });
      }
      return {
        handleImport,
        title: props.initTitle,
        noBtn: props.isBtn,
      };
    },
  });
</script>

<style lang="less" scoped></style>
