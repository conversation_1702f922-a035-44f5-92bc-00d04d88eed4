.ant-modal-root .fullscreen-modal {
  overflow: hidden;

  .ant-modal {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;

    &-content {
      height: 100%;
    }

    .ant-modal-header,
    .@{namespace}-basic-title {
      cursor: default !important;
    }
    // update-begin--author:l<PERSON><PERSON><PERSON>yang---date:20241225---for：【issues/7601】ant-design-vue@4.2.6后弹窗全屏底部有空隙
    & > div:has( > .ant-modal-content) {
      height: 100%;
    }
    // update-end--author:lia<PERSON><PERSON>yang---date:20241225---for：【issues/7601】ant-design-vue@4.2.6后弹窗全屏底部有空隙
  }
}

.ant-modal {
  width: 520px;
  padding-bottom: 0;

  .ant-modal-body > .scrollbar {
    padding: 14px;
  }

  .ant-modal-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 16px;

    .base-title {
      cursor: move !important;
    }
  }

  .ant-modal-body {
    padding: 0;

    > .scrollbar > .scrollbar__bar.is-horizontal {
      display: none;
    }
  }

  .ant-modal-large {
    top: 60px;

    &--mini {
      top: 16px;
    }
  }

  .ant-modal-header {
    padding: 16px;
  }

  .ant-modal-content {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  }

  .ant-modal-footer {
    button + button {
      margin-left: 10px;
    }
  }

  .ant-modal-close {
    font-weight: normal;
    outline: none;
  }

  .ant-modal-close-x {
    // update-begin--author:liaozhiyang---date:20241010---for：【issues/7260】原生a-modal关闭按钮位置偏移
    // display: inline-block;
    // width: 96px;
    // height: 56px;
    // line-height: 56px;
    // update-end--author:liaozhiyang---date:20241010---for：【issues/7260】原生a-modal关闭按钮位置偏移
  }

  .ant-modal-confirm-body {
    .ant-modal-confirm-content {
      // color: #fff;

      > * {
        color: @text-color-help-dark;
      }
    }
  }

  .ant-modal-confirm-confirm.error .ant-modal-confirm-body > .anticon {
    color: @error-color;
  }

  .ant-modal-confirm-btns {
    .ant-btn:last-child {
      margin-right: 0;
    }
  }

  .ant-modal-confirm-info {
    .ant-modal-confirm-body > .anticon {
      color: @warning-color;
    }
  }

  .ant-modal-confirm-confirm.success {
    .ant-modal-confirm-body > .anticon {
      color: @success-color;
    }
  }
}

.ant-modal-confirm .ant-modal-body {
  padding: 24px !important;
}
@media screen and (max-height: 600px) {
  .ant-modal {
    top: 60px;
  }
}
@media screen and (max-height: 540px) {
  .ant-modal {
    top: 30px;
  }
}
@media screen and (max-height: 480px) {
  .ant-modal {
    top: 10px;
  }
}
