import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const transmission: AppRouteModule = {
  path: '/transmission',
  name: 'Transmission',
  component: LAYOUT,
  redirect: '/transmission/dataReport',
  meta: {
    orderNo: 30,
    icon: 'ant-design:cloud-upload-outlined',
    title: '数据传输',
  },
  children: [
    {
      path: 'dataReport',
      name: 'DataReport',
      meta: {
        title: '数据上报',
        ignoreKeepAlive: true,
      },
      component: () => import('/@/views/transmission/dataReport/index.vue'),
    },
    {
      path: 'downloadChecklist',
      name: 'DownloadChecklist',
      meta: {
        title: '文件下载清单',
        ignoreKeepAlive: true,
      },
      component: () => import('/@/views/transmission/downloadChecklist/index.vue'),
    },
  ],
};

export default transmission;
