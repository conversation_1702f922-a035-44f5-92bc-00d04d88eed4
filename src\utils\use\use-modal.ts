import { Modal, LocaleProvider } from 'ant-design-vue';
import { ANT_MARK } from 'ant-design-vue/lib/locale-provider';
import type { InjectionKey, VNode } from 'vue';
import type { ModalFuncProps, ModalProps } from 'ant-design-vue';
import { getCurrentInstance, unref, render, inject, provide, h, reactive } from 'vue';
import zhCN from 'ant-design-vue/lib/locale/zh_CN';

type Callback = (...args: any) => void;

type ModalProvide = {
  onOk(cb: Callback): void;
  onCancel(cb: Callback): void;
  afterClose(cb: Callback): void;
  destroy(): void;
  update(config: ModalFuncProps & ModalProps): void;
  show(): void;
  hide(): void;
};

type ModalCustomProps = {
  size?: 'small' | 'medium' | 'large';
  cancelButtonProps?: ModalFuncProps['cancelButtonProps'] & { style?: string | Record<string, any>; hidden?: boolean };
  okButtonProps?: ModalFuncProps['okButtonProps'] & { style?: string | Record<string, any>; hidden?: boolean };
};

const MODAL: InjectionKey<ModalProvide> = Symbol('MODAL');
const modalInstanceSet = new Set<ModalProvide>();

export function useModal() {
  const appContext = getCurrentInstance()?.appContext;
  const outerModal = inject(MODAL, null);

  function getOuterModal() {
    return unref(outerModal);
  }

  function createModal(config: ModalFuncProps & ModalProps & ModalCustomProps): ModalProvide {
    const okFns: Callback[] = [];
    const cancelFns: Callback[] = [];
    const afterCloseFns: Callback[] = [];

    const container = document.createElement('div');
    let modalVNode: VNode | null;

    const currentConfig = reactive({
      visible: true,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      destroyOnClose: true,
      width: { small: 520, medium: 600, large: 940 }[config.size || 'medium'],
      bodyStyle: { maxHeight: 'calc(100vh - 108px)', overflow: 'auto' },
      dialogStyle: { maxWidth: '100vw' },
      ...config,
    });

    const modal: ModalProvide = {
      onOk,
      onCancel,
      afterClose,
      destroy,
      update,
      show,
      hide,
    };

    mount(container);

    modalInstanceSet.add(modal);

    function onOk(cb?: Callback) {
      if (typeof cb === 'function') {
        okFns.push(cb);
      }
    }

    function onCancel(cb?: Callback) {
      if (typeof cb === 'function') {
        cancelFns.push(cb);
      }
    }

    function afterClose(cb?: Callback) {
      if (typeof cb === 'function') {
        afterCloseFns.push(cb);
      }
      if (currentConfig.destroyOnClose) {
        destroy();
        modalInstanceSet.delete(modal);
      }
    }

    function update(configUpdate: ModalFuncProps & ModalProps) {
      Object.assign(currentConfig, configUpdate);
    }

    function mount(el: HTMLElement) {
      modalVNode = h({
        setup() {
          provide(MODAL, modal);

          return () => {
            const { content, onOk, onCancel, afterClose, ...others } = currentConfig as any;
            const events = {
              onOk() {
                runFns(okFns.concat(onOk));
              },
              onCancel() {
                runFns(cancelFns.concat(onCancel));
                hide();
              },
              afterClose() {
                runFns(afterCloseFns.concat(afterClose));
              },
            };

            const slots: Record<string, () => VNode> = {
              default: typeof content === 'function' ? content : () => content,
            };

            Object.keys(others).forEach((k) => {
              if (typeof others[k] === 'function') {
                slots[k] = others[k];
                delete others[k];
              }
            });

            return h(LocaleProvider, { ANT_MARK__: ANT_MARK, locale: zhCN }, () => h(Modal, { ...others, ...events }, slots));
          };
        },
      });

      modalVNode.appContext = appContext || modalVNode.appContext;
      render(modalVNode, el);
    }

    function destroy() {
      if (container) {
        container.remove();
        update({ visible: false, destroyOnClose: true });
      }
      modalVNode = null;
    }

    function show() {
      update({ visible: true });
    }

    function hide() {
      update({ visible: false });
    }

    return modal;
  }

  return {
    createModal,
    getOuterModal,
  };
}

export function destroyAllModal() {
  modalInstanceSet.forEach((item) => item.destroy());
  modalInstanceSet.clear();
}

function runFns(fns: Callback[]) {
  fns.forEach((fn) => fn?.());
}
