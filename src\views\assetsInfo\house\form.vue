<template>
  <div class="house-form">
    <div class="p-4">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="企业自定义编号" name="enterpriseCode">
                  <a-input v-model:value="formData.enterpriseCode" placeholder="请输入企业自定义编号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  name="name"
                  :label-col="{ span: 10 }"
                  :rules="[{ required: true, message: '请输入资产项目（资产名称）', trigger: 'blur' }]"
                >
                  <template #label>
                    资产项目（资产名称）
                    <a-tooltip title="系统内要求资产名称唯一">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.name" placeholder="请输入资产项目（资产名称）" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="所属集团" name="groupName">
                  <a-select v-model:value="formData.groupName" placeholder="请选择所属集团" disabled>
                    <a-select-option :value="0">厦门市城市建设发展投资有限公司</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业" name="companyName" :rules="[{ required: true, message: '请选择所属企业', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.companyName"
                    placeholder="请选择所属企业"
                    :api="getUserCompany"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="权属单位名称" name="ownUnit" :rules="[{ required: true, message: '请输入权属单位名称', trigger: 'blur' }]">
                  <a-input v-model:value="formData.ownUnit" placeholder="请输入权属单位名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产位置" name="region" :rules="[{ required: true, message: '请选择省/市/区', trigger: 'change' }]">
                  <JAreaLinkage v-model:value="formData.region" placeholder="请选择省/市/区" :showArea="true" :showAll="false" saveCode="all" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="详细地址" name="address">
                  <a-input v-model:value="formData.address" placeholder="请输入详细地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status" :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]">
                  <template #label>
                    状态
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option :value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option :value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option :value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option :value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit" :rules="[{ required: true, message: '请选择管理单位', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.manageUnit"
                    placeholder="请选择管理单位"
                    :api="getCompanyHandle"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator" :rules="[{ required: true, message: '请输入经办人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk">
                  <a-input v-model:value="formData.entryClerk" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime">
                  <a-input v-model:value="formData.createTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 房屋信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:home-outlined" class="title-icon" />
              房屋信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="houseTypes" :rules="[{ required: true, message: '请选择房屋用途（运营方式和情况）', trigger: 'change' }]">
                  <template #label>
                    房屋用途(运营方式和情况)
                    <a-tooltip title="如果是商场里面的店面时，应该勾选商业-商场，而不是商业-店面。">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <!-- house_types -->
                  <JDictSelectTag v-model:value="formData.houseTypes" :showChooseOption="false" dictCode="house_types" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产取得日期" name="gainDate" :rules="[{ required: true, message: '请选择资产取得日期', trigger: 'change' }]">
                  <a-date-picker
                    v-model:value="formData.gainDate"
                    placeholder="请选择资产取得日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产入账日期" name="assetEntryDate">
                  <a-date-picker
                    v-model:value="formData.assetEntryDate"
                    placeholder="请选择资产入账日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="具体的房屋用途"
                  name="useTypeInput"
                  :rules="[{ required: formData.houseTypes === 9, message: '请填写具体的房屋用途', trigger: 'blur' }]"
                >
                  <a-input v-model:value="formData.useTypeInput" placeholder="请输入具体的房屋用途" :disabled="formData.houseTypes !== 9" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="房屋性质" name="source" :rules="[{ required: true, message: '请选择房屋性质', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.source" :showChooseOption="false" dictCode="house_property" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="totalArea" :rules="[{ required: true, message: '请输入资产总面积', trigger: 'blur' }]">
                  <template #label>
                    资产总面积（㎡）
                    <a-tooltip title="总面积符合以下公式：（1）总面积=产权面积+非产权面积（2）总面积=可租面积+自用面积+占用面积+借用面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.totalArea"
                    placeholder="请输入资产总面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="rentableArea">
                  <template #label>
                    可租面积（㎡）
                    <a-tooltip title="可租面积符合以下公式：（1）可租面积=空置、闲置面积+出租面积（2）没有产权但是有对外出租的，也属于可租面积">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentableArea"
                    placeholder="请输入可租面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产权面积（㎡）" name="propertyArea" :rules="[{ required: true, message: '请输入产权面积', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.propertyArea"
                    placeholder="请输入产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="非产权面积（㎡）"
                  name="notPropertyArea"
                  :rules="[{ required: true, message: '请输入非产权面积', trigger: 'blur' }]"
                >
                  <a-input-number
                    v-model:value="formData.notPropertyArea"
                    placeholder="请输入非产权面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产原值（元）" name="assetsAmount" :rules="[{ required: true, message: '请输入资产原值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    placeholder="请输入资产原值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面价值（元）" name="bookAmount" :rules="[{ required: true, message: '请输入账面价值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    placeholder="请输入账面价值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="账面价值时点"
                  name="dateOfBookValue"
                  :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否有产权证" name="property" :rules="[{ required: true, message: '请选择是否有产权证', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.property" :showChooseOption="false" dictCode="land_property_type" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="产权证获得日期"
                  name="warrantDate"
                  :rules="[{ required: formData.property === 1, message: '请选择产权证获得日期', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="formData.warrantDate"
                    placeholder="请选择产权证获得日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled="formData.property === 0"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="代管委托方"
                  name="custodyEntrustingParty"
                  :rules="[{ required: formData.property === 2, message: '请输入代管委托方', trigger: 'blur' }]"
                >
                  <a-input v-model:value="formData.custodyEntrustingParty" placeholder="请输入代管委托方" :disabled="formData.property !== 2" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否账外" name="offAccount" :rules="[{ required: true, message: '请选择是否账外', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.offAccount" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否投保" name="insuranceOrNot" :rules="[{ required: true, message: '请选择是否投保', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.insuranceOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="是否抵押或质押"
                  name="mortgageOrNot"
                  :rules="[{ required: true, message: '请选择是否抵押或质押', trigger: 'change' }]"
                >
                  <JDictSelectTag v-model:value="formData.mortgageOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否危房" name="dangerousHouseOrNot" :rules="[{ required: true, message: '请选择是否危房', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.dangerousHouseOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label-col="{ span: 8 }" label="是否竣工财务结算办理" name="completionOrNot">
                  <JDictSelectTag v-model:value="formData.completionOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否拖欠工程款" name="owingOrNot">
                  <JDictSelectTag v-model:value="formData.owingOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="是否具有盘活价值"
                  :label-col="{ span: 7 }"
                  name="vitalizeOrNot"
                  :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
                >
                  <JDictSelectTag v-model:value="formData.vitalizeOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="工作进展"
                  :labelCol="{ span: 3 }"
                  name="workProgress"
                  :rules="[{ required: true, message: '请输入工作进展', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.workProgress" placeholder="请输入工作进展" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="存在问题"
                  :labelCol="{ span: 3 }"
                  name="problems"
                  :rules="[{ required: true, message: '请输入存在问题', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.problems" placeholder="请输入存在问题" :rows="4" :maxlength="300" show-count />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产使用情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:pie-chart-outlined" class="title-icon" />
              资产使用情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  :labelCol="{ span: 3 }"
                  name="assetsStatus"
                  :rules="[{ required: true, message: '请选择资产使用状态', trigger: 'change' }]"
                >
                  <template #label>
                    资产使用状态
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.assetsStatus" mode="multiple" placeholder="请选择资产使用状态" @change="handleAssetsStatusChange">
                    <a-select-option :value="0">闲置</a-select-option>
                    <a-select-option :value="1">自用</a-select-option>
                    <a-select-option :value="2">出租</a-select-option>
                    <a-select-option :value="3">出借</a-select-option>
                    <a-select-option :value="4">占用</a-select-option>
                    <a-select-option :value="5">欠租</a-select-option>
                    <a-select-option :value="6">转让</a-select-option>
                    <a-select-option :value="7">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="idleArea">
                  <template #label>
                    空置闲置面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.idleArea"
                    placeholder="请输入空置闲置面积"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    :disabled="!formData.assetsStatus.includes(0)"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="useArea">
                  <template #label>
                    自用面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.useArea"
                    placeholder="请输入自用面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(1)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="rentArea">
                  <template #label>
                    出租面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.rentArea"
                    placeholder="请输入出租面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(2)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="lendArea">
                  <template #label>
                    出借面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.lendArea"
                    placeholder="请输入出借面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(3)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="occupyArea">
                  <template #label>
                    占用面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.occupyArea"
                    placeholder="请输入占用面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(4)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="sellArea">
                  <template #label>
                    转让面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.sellArea"
                    placeholder="请输入转让面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(6)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item name="otherArea">
                  <template #label>
                    其他面积(㎡)
                    <a-tooltip
                      title="请注意各状态面积应符合以下公式，公式不包含'其他'状态，后续将取消该状态，原则上该状态面积为0：（1）总面积=空置、闲置面积+自用面积+出租面积+出借面积+占用面积（2）出租面积=专业化招租面积+非专业化招商面积（3）出租面积=厦门公开招租（进场）面积+异地公开招租（进场）面积+公开招租（非进场）面积+其他方式招租面积（4）出租面积=可租面积-空置、闲置面积"
                    >
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input-number
                    v-model:value="formData.otherArea"
                    placeholder="请输入其他面积"
                    :precision="2"
                    :min="0"
                    :disabled="!formData.assetsStatus.includes(7)"
                    :controls="false"
                    @change="validateAreaTotal"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  :labelCol="{ span: 3 }"
                  label="备注"
                  name="remark"
                  :rules="[{ required: formData.assetsStatus.includes(7), message: '资产使用状态为其他时，请输入备注', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产闲置信息 -->
        <div v-if="!isUpdate && formData.assetsStatus.includes(0)" class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:rest-outlined" class="title-icon" />
              资产闲置信息
            </div>
            <a-switch v-model:checked="showIdleInfo" checked-children="添加闲置信息" un-checked-children="不添加" />
          </div>
          <div v-if="showIdleInfo" class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="闲置起始日期"
                  name="idleStartDate"
                  :rules="[{ required: true, message: '请选择闲置起始日期', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="idleInfo.startDate"
                    placeholder="请选择闲置起始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="闲置结束日期"
                  name="idleEndDate"
                  :rules="[{ validator: validateIdleEndDate, trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="idleInfo.endDate"
                    placeholder="请选择闲置结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="(current) => idleInfo.startDate ? current && current <= dayjs(idleInfo.startDate) : false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置天数">
                  <span>{{ idleDays }} 天</span>
                  <div class="help-text">空置天数180天(含)以内的数据属于【空置信息】，空置天数180天以上的数据属于【闲置信息】</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="空置闲置面积(㎡)">
                  <a-input :value="formData.idleArea" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="闲置资产原值(元)">
                  <a-input-number
                    v-model:value="idleInfo.originalValue"
                    placeholder="请输入闲置资产原值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="闲置资产账面价值(元)"
                  name="idleBookValue"
                  :rules="[{ required: true, message: '请输入闲置资产账面价值', trigger: 'blur' }]"
                >
                  <a-input-number
                    v-model:value="idleInfo.bookValue"
                    placeholder="请输入闲置资产账面价值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="账面价值时点"
                  name="idleBookValueDate"
                  :rules="[{ required: true, message: '请选择账面价值时点', trigger: 'change' }]"
                >
                  <a-date-picker
                    v-model:value="idleInfo.bookValueDate"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item
                  label="闲置原因"
                  name="idleReason"
                  :rules="[{ required: true, message: '请输入闲置原因', trigger: 'blur' }]"
                >
                  <a-textarea v-model:value="idleInfo.reason" placeholder="请输入闲置原因" :rows="2" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="备注" :labelCol="{ span: 3 }">
                  <a-textarea v-model:value="idleInfo.remark" placeholder="请输入备注" :rows="2" />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 盘活记录 -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; margin-bottom: 10px">
              <h3 class="form-section-title">盘活记录</h3>
              <a-button type="primary" size="small" @click="addRecord">
                <template #icon>
                  <Icon icon="ant-design:plus-outlined" />
                </template>
                新增盘活记录
              </a-button>
            </div>
            <a-table :data-source="idleInfo.dealList" :pagination="false" bordered size="small">
              <a-table-column title="日期" width="180">
                <template #default="{ record, index }">
                  <a-form-item
                    :name="['idleInfo', 'dealList', index, 'date']"
                    :rules="[{ required: true, message: '请选择日期', trigger: 'change' }]"
                    class="record-table-form-item"
                  >
                    <a-date-picker
                      v-model:value="record.date"
                      placeholder="请选择日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                    />
                  </a-form-item>
                </template>
              </a-table-column>
              <a-table-column title="是否已盘活" width="150">
                <template #default="{ record, index }">
                  <a-form-item
                    :name="['idleInfo', 'dealList', index, 'isResult']"
                    :rules="[{ required: true, message: '请选择是否已盘活', trigger: 'change' }]"
                    class="record-table-form-item"
                  >
                    <a-select v-model:value="record.isResult" placeholder="请选择">
                      <a-select-option :value="0">否</a-select-option>
                      <a-select-option :value="1">是</a-select-option>
                    </a-select>
                  </a-form-item>
                </template>
              </a-table-column>
              <a-table-column title="盘活方式" width="180">
                <template #default="{ record, index }">
                  <a-form-item
                    :name="['idleInfo', 'dealList', index, 'vitalizeType']"
                    :rules="[{ required: true, message: '请选择盘活方式', trigger: 'change' }]"
                    class="record-table-form-item"
                  >
                    <a-select v-model:value="record.vitalizeType" placeholder="请选择">
                      <a-select-option :value="0">出租</a-select-option>
                      <a-select-option :value="1">出售</a-select-option>
                      <a-select-option :value="2">资产证券化</a-select-option>
                      <a-select-option :value="3">收储</a-select-option>
                      <a-select-option :value="4">转为自用</a-select-option>
                      <a-select-option :value="5">转为借用</a-select-option>
                      <a-select-option :value="6">转为占用</a-select-option>
                    </a-select>
                  </a-form-item>
                </template>
              </a-table-column>
              <a-table-column title="已采取的盘活管理措施" width="300">
                <template #default="{ record, index }">
                  <a-form-item :name="['idleInfo', 'dealList', index, 'reason']" class="record-table-form-item">
                    <a-textarea v-model:value="record.reason" placeholder="请输入已采取的盘活管理措施" :rows="2" />
                  </a-form-item>
                </template>
              </a-table-column>
              <a-table-column title="下一步建议" width="300">
                <template #default="{ record, index }">
                  <a-form-item :name="['idleInfo', 'dealList', index, 'nextReason']" class="record-table-form-item">
                    <a-textarea v-model:value="record.nextReason" placeholder="请输入下一步建议" :rows="2" />
                  </a-form-item>
                </template>
              </a-table-column>
              <a-table-column title="操作" width="80" fixed="right">
                <template #default="{ index }">
                  <a-button type="primary" danger size="small" @click="removeRecord(index)">
                    <template #icon>
                      <Icon icon="ant-design:delete-outlined" />
                    </template>
                  </a-button>
                </template>
              </a-table-column>
            </a-table>
            <div v-if="!idleInfo.dealList || idleInfo.dealList.length === 0" class="empty-text">
              暂无盘活记录，请点击上方"新增盘活记录"按钮添加。
            </div>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
          <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="house-form-component" setup>
  import { ref, reactive, computed, onMounted, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { Icon } from '/@/components/Icon';
  import { JAreaLinkage, JDictSelectTag, ApiSelect } from '/@/components/Form';
  import { saveOrUpdate, getDetail } from './house.api';
  import { useUserStore } from '/@/store/modules/user';
  import { getCompanyHandle, getUserCompany } from '/@/api/common/api'
  import dayjs from 'dayjs';

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const loading = ref(false);
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    id: undefined,
    code: '',
    enterpriseCode: '',
    name: '',
    groupName: 0,
    companyName: undefined,
    ownUnit: '',
    manageUnit: undefined,
    region: [] as any[],
    address: '',
    status: 0,
    reportOrNot: undefined,
    operator: '',
    entryClerk: '',
    createTime: '',
    houseTypes: undefined,
    useTypeInput: '',
    gainDate: undefined,
    assetEntryDate: undefined,
    source: undefined,
    totalArea: undefined,
    rentableArea: undefined,
    propertyArea: undefined,
    notPropertyArea: undefined,
    assetsAmount: undefined,
    bookAmount: undefined,
    dateOfBookValue: undefined,
    property: undefined,
    warrantDate: undefined,
    custodyEntrustingParty: '',
    offAccount: undefined,
    insuranceOrNot: undefined,
    mortgageOrNot: undefined,
    dangerousHouseOrNot: undefined,
    completionOrNot: undefined,
    owingOrNot: undefined,
    vitalizeOrNot: undefined,
    workProgress: '',
    problems: '',
    assetsStatus: [] as any[],
    idleArea: undefined,
    useArea: undefined,
    rentArea: undefined,
    lendArea: undefined,
    occupyArea: undefined,
    sellArea: undefined,
    otherArea: undefined,
    remark: '',
  });

  // 初始状态记录
  const initStatus = ref('');

  // 计算属性
  const isUpdate = computed(() => !!route.params?.id);

  // 禁用日期函数
  const disabledDate = (current) => {
    return current && current > dayjs().endOf('day');
  };

  // 房屋用途变化处理
  const handleHouseTypesChange = (value) => {
    if (value !== 9) {
      formData.useTypeInput = '';
    }
  };

  // 产权证变化处理
  const handlePropertyChange = (value) => {
    if (value === 0) {
      formData.warrantDate = undefined;
    }
    if (value !== 2) {
      formData.custodyEntrustingParty = '';
    }
  };

  // 资产使用状态变化处理
  const handleAssetsStatusChange = (values) => {
    // 清空未选中状态对应的面积
    if (!values.includes(0)) formData.idleArea = undefined;
    if (!values.includes(1)) formData.useArea = undefined;
    if (!values.includes(2)) formData.rentArea = undefined;
    if (!values.includes(3)) formData.lendArea = undefined;
    if (!values.includes(4)) formData.occupyArea = undefined;
    if (!values.includes(6)) formData.sellArea = undefined;
    if (!values.includes(7)) formData.otherArea = undefined;
  };

  // 面积总和验证
  const validateAreaTotal = () => {
    const total = formData.totalArea || 0;
    const property = formData.propertyArea || 0;
    const notProperty = formData.notPropertyArea || 0;
    const idle = formData.idleArea || 0;
    const use = formData.useArea || 0;
    const rent = formData.rentArea || 0;
    const lend = formData.lendArea || 0;
    const occupy = formData.occupyArea || 0;
    const sell = formData.sellArea || 0;
    const other = formData.otherArea || 0;

    // 验证产权面积 + 非产权面积 = 总面积
    if (property + notProperty !== total && total > 0) {
      message.warning('产权面积 + 非产权面积应等于总面积');
    }

    // 验证使用状态面积总和
    const statusAreaTotal = idle + use + rent + lend + occupy + sell + other;
    if (statusAreaTotal > total && total > 0) {
      message.warning('各使用状态面积总和不能超过总面积');
    }
  };

  // 初始化表单数据
  const initFormData = () => {
    const userInfo = userStore.getUserInfo;
    const currentDate = dayjs().format('YYYY-MM-DD');

    formData.groupName = 0;
    formData.entryClerk = userInfo.realname || '';
    formData.createTime = currentDate;
    formData.status = 0;
    formData.assetsStatus = [];
  };

  // 获取详情数据
  const getDetailData = async () => {
    if (!isUpdate.value) return;

    try {
      loading.value = true;
      const result = await getDetail(route.params.id as string);
      if (result) {
        initStatus.value = String(result.status || '');

        // 设置表单数据
        Object.keys(result).forEach(key => {
          if (key in formData) {
            formData[key] = result[key];
          }
        });

        // 处理地区数据
        if (result.provinceCode || result.cityCode || result.areaCode) {
          formData.region = [result.provinceCode, result.cityCode, result.areaCode].filter(Boolean);
        }

        // 处理资产使用状态
        if (result.assetsStatus) {
          formData.assetsStatus = Array.isArray(result.assetsStatus) ? result.assetsStatus : [result.assetsStatus];
        }
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      loading.value = true;

      // 验证表单
      await formRef.value.validate();

      // 数据处理
      const submitData: any = { ...formData };

      // 处理地区数据
      if (submitData.region && Array.isArray(submitData.region)) {
        submitData.provinceCode = submitData.region[0] || '';
        submitData.cityCode = submitData.region[1] || '';
        submitData.areaCode = submitData.region[2] || '';
      }

      // 提交数据
      await saveOrUpdate(submitData, isUpdate.value);
      message.success(isUpdate.value ? '更新成功' : '新增成功');

      // 返回列表页
      router.push('/assets-info/house');
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      loading.value = false;
    }
  };

  // 重置表单
  const handleReset = () => {
    formRef.value.resetFields();
    nextTick(() => {
      initFormData();
    });
  };

  // 组件挂载
  onMounted(() => {
    initFormData();
    getDetailData();
  });
</script>

<style lang="less" scoped>
  .house-form {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 16px;

    .simple-title {
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding: 20px;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .title-text {
        display: block;
        margin-bottom: 8px;
        font-size: 22px;
        color: #1890ff;
      }

      .title-tips {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
        font-weight: normal;

        .tip-icon {
          margin-right: 6px;
          color: #1890ff;
        }
      }
    }

    .form-card {
      background: white;
      border-radius: 6px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .form-card-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .form-card-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }

          .section-tip {
            margin-left: 8px;
            font-size: 13px;
            color: #666;
            font-weight: normal;
          }
        }
      }

      .form-card-body {
        padding: 20px;
      }
    }

    .form-footer {
      background: white;
      padding: 16px 20px;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .help-text {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
</style>
