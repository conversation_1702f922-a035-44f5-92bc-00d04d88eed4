<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <div class="pt-3px pr-3px">
      <a-alert message="请先下载导入模板，按照模板格式填写后上传" type="info" show-icon class="mb-4" />
      
      <div class="text-center mb-4">
        <a-button type="primary" @click="downloadTemplate">
          <template #icon>
            <Icon icon="ant-design:download-outlined" />
          </template>
          下载导入模板
        </a-button>
      </div>

      <BasicUpload
        :maxSize="10"
        :maxNumber="1"
        @change="handleChange"
        :api="uploadApi"
        class="my-5"
        :accept="['.xlsx', '.xls']"
        :helpText="'只能上传xlsx/xls文件，且不超过10MB'"
      />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicUpload } from '/@/components/Upload';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { importExcel, downloadTemplate as downloadTemplateApi } from './abort.api';

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const uploadList = ref<string[]>([]);

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
  });

  const getTitle = '导入租赁中止信息';

  function handleChange(list: string[]) {
    uploadList.value = list;
  }

  // 上传接口
  function uploadApi() {
    return Promise.resolve({ url: '' });
  }

  // 下载模板
  async function downloadTemplate() {
    try {
      await downloadTemplateApi();
      createMessage.success('模板下载成功');
    } catch (error) {
      createMessage.error('模板下载失败');
    }
  }

  async function handleSubmit() {
    try {
      if (uploadList.value.length === 0) {
        createMessage.warning('请先上传文件');
        return;
      }

      setModalProps({ confirmLoading: true });
      
      // 这里应该调用实际的导入接口
      await importExcel({ file: uploadList.value[0] });
      
      createMessage.success('导入成功');
      closeModal();
      emit('success');
    } catch (error) {
      createMessage.error('导入失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
