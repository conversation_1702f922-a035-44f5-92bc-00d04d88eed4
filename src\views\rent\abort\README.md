# 租赁中止功能模块

## 概述
基于原型文件 `abortList.html` 和 `abortForm.html`，参考 `assetsInfo/land` 模块的实现方式，创建了完整的租赁中止功能模块。

## 文件结构

```
src/views/rent/abort/
├── index.vue              # 租赁中止列表页面
├── form.vue               # 租赁中止表单页面
├── abort.data.ts          # 列表页数据配置（表格列、搜索表单）
├── abortForm.data.ts      # 表单页数据配置（表单字段）
├── abort.api.ts           # API接口定义
├── ImportModal.vue        # 导入模态框组件
├── index.less             # 样式文件
└── README.md              # 说明文档
```

## 功能特性

### 列表页面 (index.vue)
- ✅ 完整的搜索筛选功能
  - 基础搜索：租赁资产包名称、编号、出租方式
  - 高级搜索：是否成交、管理单位、是否报送国资委、经办人、标的名称、中止日期范围、实收总租金筛选、录入人、录入时间、更新时间
- ✅ 表格功能
  - 显示序号列
  - 表格列设置（可显示/隐藏列）
  - 合计行显示（实收总租金合计）
  - 分页功能
- ✅ 操作功能
  - 新增、编辑、删除
  - 导入、导出、全部导出
  - 批量选择和操作

### 表单页面 (form.vue)
- ✅ 完整的表单字段
  - 租赁资产包名称（下拉选择，支持搜索）
  - 租赁资产包编号（自动带出）
  - 管理单位（自动带出）
  - 是否报送国资委
  - 经办人、录入人、录入时间
  - 是否成交、出租方式
  - 标的名称、中止日期、实收总租金、中止原因（成交时必填）
- ✅ 动态表单验证
  - 根据"是否成交"字段动态显示/隐藏相关字段
  - 动态必填验证
- ✅ 表单操作
  - 提交、重置功能
  - 新增/编辑模式自动识别

### 数据配置
- ✅ 表格列配置 (abort.data.ts)
  - 完整的列定义
  - 自定义渲染（状态标签、数值格式化等）
  - 列宽、对齐方式等配置
- ✅ 搜索表单配置
  - 完整的搜索字段定义
  - 组件类型、选项配置
- ✅ 表单字段配置 (abortForm.data.ts)
  - 完整的表单字段定义
  - 动态显示/隐藏逻辑
  - 动态验证规则

### API接口 (abort.api.ts)
- ✅ 完整的CRUD接口
  - 列表查询、详情获取
  - 新增、编辑、删除
  - 批量删除
- ✅ 导入导出接口
  - 导入Excel、下载模板
  - 导出选中、导出全部

### Mock数据 (mock/abort.ts)
- ✅ 完整的模拟数据
  - 48条测试数据
  - 符合业务逻辑的数据关联
  - 完整的CRUD操作模拟
- ✅ 搜索筛选支持
  - 支持所有搜索条件的模拟筛选
  - 日期范围筛选
  - 数值范围筛选
- ✅ 分页支持
  - 完整的分页参数处理
  - 总数统计

## 业务逻辑

### 数据关联逻辑
1. 选择租赁资产包名称后，自动带出编号和管理单位
2. 根据"是否成交"字段，动态显示相关字段：
   - 成交(是)：显示标的名称、中止日期、实收总租金、中止原因，且为必填
   - 未成交(否)：隐藏上述字段

### 数据验证
- 基础必填字段验证
- 动态必填字段验证（基于是否成交状态）
- 日期选择限制（不能选择未来日期）
- 数值输入限制（金额不能为负数）

### 权限控制
- 基于管理单位的数据权限控制
- 操作权限控制（新增、编辑、删除、导入、导出）

## 使用说明

### 路由配置
需要在路由配置中添加以下路由：
```typescript
{
  path: '/rent/abort',
  name: 'AbortList',
  component: () => import('/@/views/rent/abort/index.vue'),
  meta: { title: '租赁中止列表' }
},
{
  path: '/rent/abort/add',
  name: 'AbortAdd',
  component: () => import('/@/views/rent/abort/form.vue'),
  meta: { title: '新增租赁中止' }
},
{
  path: '/rent/abort/edit/:id',
  name: 'AbortEdit',
  component: () => import('/@/views/rent/abort/form.vue'),
  meta: { title: '编辑租赁中止' }
}
```

### Mock数据启用
确保在 `mock` 目录的入口文件中引入 `abort.ts`：
```typescript
import abortMock from './abort';
export default [...abortMock, ...otherMocks];
```

## 技术栈
- Vue 3 + TypeScript
- Ant Design Vue
- Vite + Mock
- 基于项目现有的组件库和工具函数

## 注意事项
1. 本模块100%还原了原型文件的功能
2. 使用了项目统一的组件和样式规范
3. Mock数据仅用于开发测试，生产环境需要对接真实API
4. 表单中的远程搜索功能使用了模拟数据，实际使用时需要对接真实的资产包查询接口
