import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '租赁资产包名称',
    dataIndex: 'name',
    width: 200,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '租赁资产包编号',
    dataIndex: 'code',
    width: 150,
    fixed: 'left',
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 240,
    customRender: ({ text }) => {
      const unitMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司'
      };
      return unitMap[text] || '-';
    },
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return text === 1 ? render.renderTag('是', 'success') : render.renderTag('否', 'default');
    },
  },
  {
    title: '是否成交',
    dataIndex: 'abortStatus',
    width: 100,
    customRender: ({ text }) => {
      return text === 1 ? render.renderTag('是', 'success') : render.renderTag('否', 'warning');
    },
  },
  {
    title: '出租方式',
    dataIndex: 'rentType',
    width: 180,
    customRender: ({ text }) => {
      const rentTypeMap = {
        0: '厦门公开招租（进场）',
        1: '异地公开招租（进场）',
        2: '公开招租（非进场）',
        3: '其他招租'
      };
      return rentTypeMap[text] || '-';
    },
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '中止日期',
    dataIndex: 'abortDate',
    width: 120,
  },
  {
    title: '实收总租金(万元)',
    dataIndex: 'actTotalRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '中止原因',
    dataIndex: 'abortReason',
    width: 250,
    ellipsis: true,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '租赁资产包名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包名称',
    },
  },
  {
    label: '租赁资产包编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包编号',
    },
  },
  {
    label: '出租方式',
    field: 'rentType',
    component: 'Select',
    componentProps: {
      placeholder: '请选择出租方式',
      options: [
        { label: '厦门公开招租（进场）', value: 0 },
        { label: '异地公开招租（进场）', value: 1 },
        { label: '公开招租（非进场）', value: 2 },
        { label: '其他招租', value: 3 },
      ],
    },
  },
  {
    label: '是否成交',
    field: 'abortStatus',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否成交',
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'Select',
    componentProps: {
      placeholder: '请选择管理单位',
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'Select',
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '标的名称',
    field: 'targetName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
  {
    label: '中止日期范围',
    field: 'abortDateRange',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '实收总租金大于',
    field: 'minActTotalRent',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入金额(万元)',
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '更新时间',
    field: 'updateTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
