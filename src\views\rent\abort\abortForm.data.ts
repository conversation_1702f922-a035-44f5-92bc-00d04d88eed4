import { FormSchema } from '/@/components/Table';
import { h } from 'vue';

// 企业选项
const enterpriseOptions = [
  { label: '厦门市城市建设发展投资有限公司', value: '厦门市城市建设发展投资有限公司' },
  { label: '厦门市地热资源管理有限公司', value: '厦门市地热资源管理有限公司' },
  { label: '厦门兴地房屋征迁服务有限公司', value: '厦门兴地房屋征迁服务有限公司' },
  { label: '厦门地丰置业有限公司', value: '厦门地丰置业有限公司' },
  { label: '图智策划咨询（厦门）有限公司', value: '图智策划咨询（厦门）有限公司' },
  { label: '厦门市集众祥和物业管理有限公司', value: '厦门市集众祥和物业管理有限公司' },
  { label: '厦门市人居乐业物业服务有限公司', value: '厦门市人居乐业物业服务有限公司' }
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '租赁资产包名称',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择租赁资产包名称',
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      options: (() => {
        const mockData = [];
        for (let i = 1; i <= 10; i++) {
          const enterprise = enterpriseOptions[Math.floor(Math.random() * enterpriseOptions.length)];
          mockData.push({
            label: `资产包${i}（ZCBH${i}000${i}）`,
            value: `资产包${i}`,
            code: `ZCBH${i}000${i}`,
            manageUnit: enterprise.value
          });
        }
        return mockData;
      })(),
      onChange: (value, option) => {
        // 这里可以处理选择变化事件，自动填充编号和管理单位
        console.log('Selected:', value, option);
      },
    },
    colProps: { span: 12 },
  },
  {
    field: 'code',
    label: '租赁资产包编号',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '选择租赁资产包名称后自动带出',
      disabled: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '选择租赁资产包后自动带出',
      disabled: true,
    },
    colProps: { span: 12 },
    helpMessage: '将使用管理单位作为数据权限判断依据',
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入经办人',
    },
    colProps: { span: 12 },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '录入人',
      disabled: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '录入时间',
      disabled: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'abortStatus',
    label: '是否成交',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择是否成交',
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'rentType',
    label: '出租方式',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择出租方式',
      options: [
        { label: '厦门公开招租（进场）', value: 0 },
        { label: '异地公开招租（进场）', value: 1 },
        { label: '公开招租（非进场）', value: 2 },
        { label: '其他招租', value: 3 },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'targetName',
    label: '标的名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => values.abortStatus === 1,
    dynamicRules: ({ values }) => {
      return values.abortStatus === 1 ? [{ required: true, message: '请输入标的名称' }] : [];
    },
  },
  {
    field: 'abortDate',
    label: '中止日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择中止日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      disabledDate: (current) => {
        return current && current > new Date();
      },
      style: { width: '100%' },
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => values.abortStatus === 1,
    dynamicRules: ({ values }) => {
      return values.abortStatus === 1 ? [{ required: true, message: '请选择中止日期' }] : [];
    },
  },
  {
    field: 'actTotalRent',
    label: '实收总租金（万元）',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入实收总租金',
      min: 0,
      precision: 2,
      controls: false,
      style: { width: '100%' },
    },
    colProps: { span: 12 },
    ifShow: ({ values }) => values.abortStatus === 1,
    dynamicRules: ({ values }) => {
      return values.abortStatus === 1 ? [{ required: true, message: '请输入实收总租金' }] : [];
    },
  },
  {
    field: 'abortReason',
    label: '中止原因',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入中止原因',
      rows: 4,
    },
    colProps: { span: 24 },
    ifShow: ({ values }) => values.abortStatus === 1,
    dynamicRules: ({ values }) => {
      return values.abortStatus === 1 ? [{ required: true, message: '请输入中止原因' }] : [];
    },
  },
];
