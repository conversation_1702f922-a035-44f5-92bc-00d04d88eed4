<template>
  <div class="abort-form">
    <div class="simple-title">租赁中止表单</div>
    <div class="p-4">
      <!-- 租赁中止信息 -->
      <div class="form-card">
        <div class="form-card-header">
          <div class="form-card-title">
            <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
            租赁中止信息
          </div>
        </div>
        <div class="form-card-body">
          <BasicForm @register="registerForm" :schemas="formSchema" />
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
        <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="AbortForm" setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { Icon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formSchema } from './abortForm.data';
  import { saveOrUpdate, getDetail } from './abort.api';

  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  // 表单数据
  const formData = ref<any>({});

  // 表单
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields }] = useForm({
    labelWidth: 200,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
    rowProps: { gutter: 20 },
  });

  /**
   * 初始化
   */
  onMounted(async () => {
    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadDetail();
    } else {
      isUpdate.value = false;
      // 设置默认值
      setFieldsValue({
        reportOrNot: 1,
        operator: '张三',
        entryClerk: '张三',
        createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      });
    }
  });

  /**
   * 加载详情
   */
  async function loadDetail() {
    try {
      const data = await getDetail(recordId.value);
      if (data) {
        formData.value = { ...data };
        setFieldsValue(data);
      }
    } catch (error) {
      createMessage.error('加载详情失败');
    }
  }

  /**
   * 提交表单
   */
  async function handleSubmit() {
    try {
      // 验证表单
      const values = await validate();

      if (!values) {
        return;
      }

      // 如果是成交状态，需要额外验证相关字段
      if (values.abortStatus === 1) {
        if (!values.targetName || !values.abortReason || 
            !values.abortDate || !values.actTotalRent) {
          createMessage.error('成交状态下，标的名称、中止日期、实收总租金、中止原因为必填项');
          return;
        }
      }

      // 合并表单数据
      const submitData = {
        ...values,
        id: isUpdate.value ? recordId.value : undefined,
      };

      loading.value = true;
      await saveOrUpdate(submitData, isUpdate.value);

      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');
      router.push('/rent/abort');
    } catch (error) {
      createMessage.error(isUpdate.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重置表单
   */
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '是否确认重置表单数据？',
      iconType: 'warning',
      onOk: () => {
        if (isUpdate.value) {
          loadDetail();
        } else {
          resetFields();
          setFieldsValue({
            reportOrNot: 1,
            operator: '张三',
            entryClerk: '张三',
            createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
          });
        }
      },
    });
  }
</script>
  
<style lang="less" scoped>
  .abort-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 统一设置表单项标签宽度
    :deep(.ant-form-item-label) {
      width: 180px;
      min-width: 180px;
      text-align: right;
      padding-right: 8px;
    }

    :deep(.ant-form-item-label > label) {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>
