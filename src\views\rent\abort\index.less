// 租赁中止列表页样式
.abort-list {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }

  .ant-table-fixed-right {
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.12);
  }

  // 状态标签样式
  .status-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .status-yes {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  .status-no {
    background-color: #fff7e6;
    color: #faad14;
    border: 1px solid #ffd591;
  }

  // 截断长文本
  .truncate {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 合计行样式
  .summary-row {
    background-color: #f5f7fa !important;
    font-weight: normal;
  }

  .summary-row td {
    background-color: #f5f7fa !important;
  }

  // 合计行的首列文本加粗
  .summary-row .summary-title {
    font-weight: bold;
    color: #303133;
  }

  // 数值列右对齐
  .number-cell {
    text-align: right !important;
  }
}
