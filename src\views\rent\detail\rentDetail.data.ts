import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '租赁资产包编号',
    dataIndex: 'packageCode',
    width: 180,
    fixed: 'left',
  },
  {
    title: '租赁资产包名称',
    dataIndex: 'packageName',
    width: 180,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 120,
    customRender: ({ text }) => {
      const typeMap = {
        'land': '土地',
        'house': '房屋',
        'ad': '广告位',
        'equipment': '设备',
        'other': '其他'
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '标的所占面积(㎡)',
    dataIndex: 'area',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '成交日期',
    dataIndex: 'dealDate',
    width: 120,
  },
  {
    title: '承租开始时间',
    dataIndex: 'leaseStartDate',
    width: 140,
  },
  {
    title: '承租结束时间',
    dataIndex: 'leaseEndDate',
    width: 140,
  },
  {
    title: '拟收总租金(万元)',
    dataIndex: 'totalRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '年份',
    dataIndex: 'year',
    width: 100,
  },
  {
    title: '季度',
    dataIndex: 'quarter',
    width: 100,
  },
  {
    title: '应收租金(万元)',
    dataIndex: 'expectedRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '实收租金(万元)',
    dataIndex: 'actualRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '未收租金(万元)',
    dataIndex: 'unpaidRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '违约租金(万元)',
    dataIndex: 'penaltyRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '减免租金(万元)',
    dataIndex: 'discountRent',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '管理单位',
    dataIndex: 'companyName',
    width: 200,
    customRender: ({ text }) => {
      const companyMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司'
      };
      return companyMap[text] || text;
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产包名称',
    field: 'packageName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包名称',
    },
  },
  {
    label: '资产包编号',
    field: 'packageCode',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租赁资产包编号',
    },
  },
  {
    label: '出租方式',
    field: 'rentMethod',
    component: 'Select',
    componentProps: {
      placeholder: '请选择出租方式',
      options: [
        { label: '厦门公开招租（进场）', value: 'xiamen_public' },
        { label: '异地公开招租（进场）', value: 'other_place_public' },
        { label: '公开招租（非进场）', value: 'public_not_enter' },
        { label: '其他招租', value: 'other_rent' },
        { label: '公开招租（续租）', value: 'public_renew' },
        { label: '其他方式招租（专业化招商）', value: 'other_professional' },
        { label: '其他方式招租（协议）', value: 'other_agreement' },
      ],
    },
  },
  {
    label: '资产类型',
    field: 'assetType',
    component: 'Select',
    componentProps: {
      placeholder: '请选择资产类型',
      options: [
        { label: '土地', value: 'land' },
        { label: '房屋', value: 'house' },
        { label: '广告位', value: 'ad' },
        { label: '设备', value: 'equipment' },
        { label: '其他', value: 'other' },
      ],
    },
  },
  {
    label: '标的名称',
    field: 'targetName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
  {
    label: '管理单位',
    field: 'companyName',
    component: 'Select',
    componentProps: {
      placeholder: '请选择管理单位',
      mode: 'multiple',
      maxTagCount: 2,
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
  },
  {
    label: '季度范围',
    field: 'quarterRange',
    component: 'RangePicker',
    componentProps: {
      picker: 'quarter',
      placeholder: ['开始季度', '结束季度'],
      format: 'YYYY-MM',
      valueFormat: 'YYYY-MM',
      style: { width: '100%' },
    },
  },
];
