import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/areaPreview/list',
  export = '/mock/areaPreview/export',
}

// 面积数据接口类型定义
export interface AreaPreviewItem {
  assetType: string;
  propertyRight: string;
  propertyRightText: string;
  totalArea: number;
  propertyRightArea: number;
  nonPropertyRightArea: number;
  rentableArea: number;
  rentedArea: number;
  idleArea: number;
  idleAreaRatio: number;
  rentRatio: number;
  professionalArea: number;
  nonProfessionalArea: number;
  xiamenPublicRentArea: number;
  otherPlacePublicRentArea: number;
  nonEntryPublicRentArea: number;
  otherRentMethodArea: number;
  temporaryIdleArea: number;
  idleOver6MonthsArea: number;
  idleOver6MonthsRatio: number;
  selfUsedArea: number;
  occupiedArea: number;
  borrowedArea: number;
  usableArea?: number; // 仅房屋资产有此字段
  companyName?: string; // 所属企业名称
}

/**
 * 获取面积一览表数据
 * @param params
 */
export const getAreaPreviewList = (params?: any): Promise<AreaPreviewItem[]> => 
  defHttp.get({ url: Api.list, params });

/**
 * 导出面积一览表数据
 * @param params
 */
export const exportAreaPreview = (params?: any) => 
  defHttp.get({ url: Api.export, params, responseType: 'blob' });
