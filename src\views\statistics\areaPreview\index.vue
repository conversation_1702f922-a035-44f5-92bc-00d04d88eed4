<template>
  <div class="area-preview-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">面积一览表</h1>
    </div>

    <!-- 搜索与筛选区 -->
    <a-card :bordered="false" class="search-area">
      <BasicForm @register="registerForm" @submit="handleSearch" />
    </a-card>

    <!-- 操作按钮区 -->
    <div class="button-area">
      <a-button type="default" @click="handleUpdateData">
        <template #icon><ReloadOutlined /></template>
        更新数据
      </a-button>
      <a-button type="default" @click="showDataDescription">
        <template #icon><InfoCircleOutlined /></template>
        数据说明
      </a-button>
      <a-button type="primary" @click="handleExport">
        <template #icon><DownloadOutlined /></template>
        导出
      </a-button>
    </div>

    <!-- 数据展示区 -->
    <div class="data-grid">
      <a-card v-for="(item, index) in currentAreaData" :key="index" :bordered="false" class="data-card" :body-style="{ padding: '20px' }">
        <template #title>
          <div class="card-title">
            <component :is="getAssetTypeIcon(item.assetType)" :style="{ marginRight: '8px', color: getAssetTypeColor(item.assetType) }" />
            {{ item.assetType }}
            <a-tag v-if="item.propertyRightText" :color="getPropertyRightTagColor(item.propertyRight)" style="margin-left: 10px" size="small">
              {{ item.propertyRightText }}
            </a-tag>
          </div>
        </template>

        <!-- 数据项网格 -->
        <div class="data-items-grid">
          <!-- 总面积 -->
          <div class="data-item">
            <span class="data-item-label">总面积(万㎡)</span>
            <span class="data-item-value highlight-value">{{ formatArea(item.totalArea) }}</span>
          </div>

          <!-- 产权/非产权面积 -->
          <div class="data-item">
            <span class="data-item-label">产权面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.propertyRightArea) }}</span>
          </div>

          <div class="data-item">
            <span class="data-item-label">非产权面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.nonPropertyRightArea) }}</span>
          </div>

          <!-- 可租面积 -->
          <div class="data-item">
            <span class="data-item-label">可租面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.rentableArea) }}</span>
          </div>

          <!-- 已租面积 -->
          <div class="data-item">
            <span class="data-item-label">已租面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.rentedArea) }}</span>
          </div>

          <!-- 闲置面积 -->
          <div class="data-item">
            <span class="data-item-label">闲置面积(万㎡)</span>
            <span class="data-item-value" :class="{ 'warning-value': item.idleAreaRatio > 20 }">
              {{ formatArea(item.idleArea) }}
            </span>
          </div>

          <!-- 出租率 -->
          <div class="data-item">
            <span class="data-item-label">出租率</span>
            <span class="data-item-value" :class="getRentRatioClass(item.rentRatio)">
              {{ formatPercent(item.rentRatio) }}
            </span>
          </div>

          <!-- 出租率进度条 -->
          <div style="grid-column: 1 / -1; margin-top: 8px">
            <a-progress :percent="item.rentRatio" :stroke-color="getRentRatioColor(item.rentRatio)" :show-info="false" size="small" />
            <div class="progress-info">
              <span>0%</span>
              <span>100%</span>
            </div>
          </div>

          <!-- 专业化招商面积 -->
          <div class="data-item">
            <span class="data-item-label">专业化招商面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.professionalArea) }}</span>
          </div>

          <!-- 非专业化招商面积 -->
          <div class="data-item">
            <span class="data-item-label">非专业化招商面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.nonProfessionalArea) }}</span>
          </div>

          <!-- 厦门公开招租(进场)面积 -->
          <div class="data-item">
            <span class="data-item-label">厦门公开招租(进场)面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.xiamenPublicRentArea) }}</span>
          </div>

          <!-- 异地公开招租(进场)面积 -->
          <div class="data-item">
            <span class="data-item-label">异地公开招租(进场)面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.otherPlacePublicRentArea) }}</span>
          </div>

          <!-- 公开招租(非进场)面积 -->
          <div class="data-item">
            <span class="data-item-label">公开招租(非进场)面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.nonEntryPublicRentArea) }}</span>
          </div>

          <!-- 其他方式招租面积 -->
          <div class="data-item">
            <span class="data-item-label">其他方式招租面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.otherRentMethodArea) }}</span>
          </div>

          <!-- 临时面积 -->
          <div class="data-item">
            <span class="data-item-label">临时面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.temporaryIdleArea) }}</span>
          </div>

          <!-- 6个月以上闲置面积 -->
          <div class="data-item">
            <span class="data-item-label">6个月以上闲置面积(万㎡)</span>
            <span
              class="data-item-value"
              :class="{ 'warning-value': item.idleOver6MonthsRatio > 10, 'danger-value': item.idleOver6MonthsRatio > 20 }"
            >
              {{ formatArea(item.idleOver6MonthsArea) }}
            </span>
          </div>

          <!-- 闲置率（6个月以上） -->
          <div class="data-item">
            <span class="data-item-label">闲置率(6个月以上)</span>
            <span
              class="data-item-value"
              :class="{ 'warning-value': item.idleOver6MonthsRatio > 10, 'danger-value': item.idleOver6MonthsRatio > 20 }"
            >
              {{ formatPercent(item.idleOver6MonthsRatio) }}
            </span>
          </div>

          <!-- 自用面积 -->
          <div class="data-item">
            <span class="data-item-label">自用面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.selfUsedArea) }}</span>
          </div>

          <!-- 占用面积 -->
          <div class="data-item">
            <span class="data-item-label">占用面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.occupiedArea) }}</span>
          </div>

          <!-- 借用面积 -->
          <div class="data-item">
            <span class="data-item-label">借用面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.borrowedArea) }}</span>
          </div>

          <!-- 可使用面积（仅房屋资产显示） -->
          <div v-if="item.assetType === '房屋' && item.usableArea" class="data-item">
            <span class="data-item-label">可使用面积(万㎡)</span>
            <span class="data-item-value">{{ formatArea(item.usableArea) }}</span>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, h } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { InfoCircleOutlined, DownloadOutlined, HomeOutlined, BuildOutlined } from '@ant-design/icons-vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { getAreaPreviewList, exportAreaPreview, type AreaPreviewItem } from './areaPreview.api';
  import { searchFormSchema } from './areaPreview.data';

  // 注册BasicForm
  const [registerForm] = useForm({
    schemas: searchFormSchema,
    layout: 'inline',
    showActionButtonGroup: true,
    submitButtonOptions: {
      text: '查询',
      preIcon: 'ant-design:search-outlined',
    },
    resetButtonOptions: {
      text: '重置',
      preIcon: 'ant-design:reload-outlined',
    },
    actionColOptions: {
      span: 4,
    },
    baseColProps: {
      style: { paddingRight: '16px' },
    },
  });

  // 面积数据
  const areaData = ref<AreaPreviewItem[]>([]);
  const filteredAreaData = ref<AreaPreviewItem[]>([]);

  // 计算当前展示的数据
  const currentAreaData = computed(() => {
    return filteredAreaData.value.length > 0 ? filteredAreaData.value : areaData.value;
  });

  // 处理查询
  const handleSearch = (values: any) => {
    filteredAreaData.value = areaData.value.filter((item) => {
      const matchesAssetType = !values.assetType ||
        item.assetType === (values.assetType === 'land' ? '土地' : '房屋');
      const matchesPropertyRight = !values.propertyRight ||
        item.propertyRight === values.propertyRight;
      const matchesCompanies = !values.companies?.length ||
        (item.companyName && values.companies.includes(item.companyName));
      return matchesAssetType && matchesPropertyRight && matchesCompanies;
    });
  };

  // 处理更新数据
  const handleUpdateData = () => {
    Modal.confirm({
      title: '提示',
      content: '确定要更新数据吗?',
      onOk() {
        loadData();
        message.success('数据已更新');
      },
    });
  };

  // 显示数据说明对话框
  const showDataDescription = () => {
    const dataDescriptions = [
      { label: '总面积(万㎡)', desc: '土地或房屋资产，各个资产的"资产总面积(㎡)"字段累加，并进行单位换算' },
      { label: '产权面积(万㎡)', desc: '土地或房屋资产，各个资产的"产权面积(㎡)"字段累加，并进行单位换算' },
      { label: '非产权面积(万㎡)', desc: '土地或房屋资产，各个资产的"非产权面积(㎡)"字段累加，并进行单位换算' },
      { label: '可租面积(万㎡)', desc: '土地或房屋资产，各个资产的"资产可租面积(㎡)"字段累加，并进行单位换算' },
      { label: '已租面积(万㎡)', desc: '土地或房屋资产，各个资产的"各状态面积统计(㎡)-出租"字段累加，并进行单位换算' },
      { label: '出租率', desc: '根据公式"出租率=已租面积/可租面积"计算' },
      { label: '专业化招商面积(万㎡)', desc: '取专业化招商数据，关联资产模块"标的所占面积(㎡)"字段累加，并进行单位换算' },
      { label: '非专业化招商面积(万㎡)', desc: '根据"非专业化招商面积=已租面积-专业化招商面积"计算，并进行单位换算' },
      { label: '厦门公开招租(进场)面积(万㎡)', desc: '根据【厦门公开招租（进场）】模块，关联资产模块"标的所占面积(㎡)"字段累加，并进行单位换算' },
      { label: '异地公开招租(进场)面积(万㎡)', desc: '根据【异地公开招租(进场)】模块，关联资产模块"标的所占面积(㎡)"字段累加，并进行单位换算' },
      { label: '公开招租(非进场)面积(万㎡)', desc: '根据【公开招租(非进场)】模块，关联资产模块"标的所占面积(㎡)"字段累加，并进行单位换算' },
      { label: '其他方式招租面积(万㎡)', desc: '根据【其他招租】模块，关联资产模块"标的所占面积(㎡)"字段累加，并进行单位换算' },
      { label: '临时面积(万㎡)', desc: '闲置管理中，当前闲置资产中，闲置时长"6个月内"闲置面积累加，并进行单位换算' },
      { label: '6个月以上闲置面积(万㎡)', desc: '闲置管理中，当前闲置资产中，闲置时长"6个月以上"闲置面积累加，并进行单位换算' },
      { label: '闲置率(6个月以上)', desc: '闲置率=闲置6个月以上面积/资产总面积' },
      { label: '自用面积(万㎡)', desc: '土地或房屋资产，各个资产的"各状态面积统计(㎡)-自用"字段累加，并进行单位换算' },
      { label: '占用面积(万㎡)', desc: '土地或房屋资产，各个资产的"各状态面积统计(㎡)-占用"字段累加，并进行单位换算' },
      { label: '借用面积(万㎡)', desc: '土地或房屋资产，各个资产的"各状态面积统计(㎡)-借用"字段累加，并进行单位换算' },
    ];

    Modal.info({
      title: '数据说明',
      width: 800,
      content: h('div', { style: { maxHeight: '400px', overflowY: 'auto', textAlign: 'left' } }, [
        h('h3', { style: { marginTop: 0, color: '#1890ff' } }, '面积数据项说明'),
        h('div', { style: { paddingLeft: '20px' } },
          dataDescriptions.map((item, index) =>
            h('div', { key: index, style: { marginBottom: '12px', lineHeight: '1.6' } }, [
              h('span', { style: { fontWeight: 'bold', color: '#262626' } }, `${item.label}：`),
              h('span', { style: { color: '#595959' } }, item.desc)
            ])
          )
        )
      ]),
    });
  };

  // 处理导出
  const handleExport = async () => {
    try {
      await exportAreaPreview();
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 格式化面积数字
  const formatArea = (area: number) => {
    return area ? area.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  };

  // 格式化百分比
  const formatPercent = (value: number) => {
    return value ? value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + '%' : '0.00%';
  };

  // 获取资产类型图标
  const getAssetTypeIcon = (type: string) => {
    return type === '土地' ? HomeOutlined : BuildOutlined;
  };

  // 获取资产类型颜色
  const getAssetTypeColor = (type: string) => {
    return type === '土地' ? '#52c41a' : '#1890ff';
  };

  // 获取产权标签颜色
  const getPropertyRightTagColor = (propertyRight: string) => {
    const colorMap = {
      yes: 'success',
      no: 'default',
      agent: 'warning',
    };
    return colorMap[propertyRight] || 'default';
  };

  // 获取出租率CSS类
  const getRentRatioClass = (ratio: number) => {
    if (ratio >= 80) return 'highlight-value';
    if (ratio >= 60) return '';
    if (ratio >= 40) return 'warning-value';
    return 'danger-value';
  };

  // 获取出租率颜色
  const getRentRatioColor = (ratio: number) => {
    if (ratio >= 80) return '#1890ff';
    if (ratio >= 60) return '#52c41a';
    if (ratio >= 40) return '#faad14';
    return '#f5222d';
  };

  // 加载数据
  const loadData = async () => {
    try {
      const response = await getAreaPreviewList();
      areaData.value = response;
    } catch (error) {
      message.error('数据加载失败');
    }
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .area-preview-container {
    padding: 20px;
    background-color: #f0f2f5;
    min-height: 100vh;

    .page-header {
      margin-bottom: 20px;
      padding: 0 0 16px 0;
      border-bottom: 1px solid #d9d9d9;

      .page-title {
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        margin: 0;
      }
    }

    .search-area {
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .button-area {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;
    }

    // 数据卡片网格布局
    .data-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      width: 100%;
    }

    // 数据卡片样式
    .data-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    // 数据项网格布局
    .data-items-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px 12px;
    }

    .data-item {
      margin-bottom: 6px;
      display: flex;
      justify-content: space-between;

      .data-item-label {
        color: #666;
        font-size: 12px;
        padding-right: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .data-item-value {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        font-size: 12px;
        white-space: nowrap;

        &.highlight-value {
          color: #1890ff;
        }

        &.warning-value {
          color: #faad14;
        }

        &.danger-value {
          color: #f5222d;
        }
      }
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
      margin-top: 3px;
    }

    // 响应式布局
    @media (max-width: 1400px) {
      .data-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 992px) {
      .data-items-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .data-grid {
        grid-template-columns: 1fr;
      }

      .button-area {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
      }
    }
  }
</style>
