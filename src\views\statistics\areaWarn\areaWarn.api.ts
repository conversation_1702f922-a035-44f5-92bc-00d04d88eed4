import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/areaWarn/list',
  export = '/mock/areaWarn/export',
}

// 面积统计预警数据接口类型定义
export interface AreaWarnItem {
  companyName: string;
  assetType: string;
  assetCode: string;
  assetName: string;
  warningContent: string;
}

// 分页查询参数
export interface AreaWarnListParams {
  page?: number;
  pageSize?: number;
  companies?: string[];
}

// 分页响应数据
export interface AreaWarnListResult {
  records: AreaWarnItem[];
  total: number;
  pageNo: number;
  pageSize: number;
}

/**
 * 获取面积统计预警数据
 * @param params
 */
export const getAreaWarnList = (params?: AreaWarnListParams) =>
  defHttp.get({ url: Api.list, params });

/**
 * 导出面积统计预警数据
 * @param params
 */
export const exportAreaWarn = (params?: any) => 
  defHttp.get({ url: Api.export, params, responseType: 'blob' });
