import { FormSchema } from '/@/components/Form';
import { BasicColumn } from '/@/components/Table';

// 查询表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'companies',
    label: '所属企业',
    component: 'Select',
    componentProps: {
      placeholder: '请选择所属企业',
      allowClear: true,
      mode: 'multiple',
      maxTagCount: 2,
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: '厦门市城市建设发展投资有限公司' },
        { label: '厦门市地热资源管理有限公司', value: '厦门市地热资源管理有限公司' },
        { label: '厦门兴地房屋征迁服务有限公司', value: '厦门兴地房屋征迁服务有限公司' },
        { label: '厦门地丰置业有限公司', value: '厦门地丰置业有限公司' },
        { label: '图智策划咨询（厦门）有限公司', value: '图智策划咨询（厦门）有限公司' },
        { label: '厦门市集众祥和物业管理有限公司', value: '厦门市集众祥和物业管理有限公司' },
        { label: '厦门市人居乐业物业服务有限公司', value: '厦门市人居乐业物业服务有限公司' },
      ],
    },
    colProps: { span: 8 },
  },
];

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: ({ index }) => index + 1,
  },
  {
    title: '企业名称',
    dataIndex: 'companyName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 100,
    align: 'center',
  },
  {
    title: '资产编号',
    dataIndex: 'assetCode',
    width: 180,
    ellipsis: true,
  },
  {
    title: '资产名称',
    dataIndex: 'assetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '预警内容',
    dataIndex: 'warningContent',
    width: 300,
    ellipsis: true,
  },
];
