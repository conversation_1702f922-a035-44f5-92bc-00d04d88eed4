<template>
  <div :class="prefixCls">
    <!-- 引用表格 -->
    <BasicTable @register="registerTable">
      <!-- 表格头部工具栏 -->
      <template #tableTitle>
        <a-button type="primary" @click="handleExport" preIcon="ant-design:download-outlined">
          导出
        </a-button>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { message } from 'ant-design-vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { getAreaWarnList, exportAreaWarn } from './areaWarn.api';
  import { searchFormSchema, columns } from './areaWarn.data';

  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    designScope: 'area-warn-list',
    tableProps: {
      title: '资产面积统计预警',
      api: getAreaWarnList,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      showActionColumn: false,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'area_warn_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 100,
        // 显示展开/收起按钮
        showAdvancedButton: false,
        // 超过3列时默认折叠
        autoAdvancedCol: 3,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      beforeFetch: (params) => {
        return {
          ...params,
          page: params.page || 1,
          pageSize: params.pageSize || 10,
        };
      },
      fetchSetting: {
        pageField: 'page',
        sizeField: 'pageSize',
        listField: 'records',
        totalField: 'total',
      },
    },
    exportConfig: {
      name: '资产面积统计预警',
      url: () => '/mock/areaWarn/export',
    },
  });

  // 注册table数据
  const [registerTable, { getForm }] = tableContext;

  // 处理导出
  const handleExport = async () => {
    try {
      const { getFieldsValue } = getForm();
      const searchParams = getFieldsValue();
      await exportAreaWarn(searchParams);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };
</script>

<style lang="less" scoped>
  // 使用系统默认样式，无需额外样式
</style>
