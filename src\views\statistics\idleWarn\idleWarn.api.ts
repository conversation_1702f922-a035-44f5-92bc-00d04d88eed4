import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/idleWarn/list',
  export = '/mock/idleWarn/export',
}

// 空置闲置预警数据接口类型定义
export interface IdleWarnItem {
  assetType: string;
  propertyRight: string;
  propertyRightText: string;
  totalArea: number;
  idleOver6MonthsArea: number;
  idleRateOver6Months: number;
  idleUnder6MonthsArea: number;
  idleRateUnder6Months: number;
  idle6To12MonthsArea: number;
  idle1To3YearsArea: number;
  idleOver3YearsArea: number;
  idleAssetsCountOver6Months: number;
  idleRateExcludeAgent: number;
  companyName?: string; // 所属企业名称
}

/**
 * 获取空置闲置预警数据
 * @param params
 */
export const getIdleWarnList = (params?: any): Promise<IdleWarnItem[]> => 
  defHttp.get({ url: Api.list, params });

/**
 * 导出空置闲置预警数据
 * @param params
 */
export const exportIdleWarn = (params?: any) => 
  defHttp.get({ url: Api.export, params, responseType: 'blob' });
