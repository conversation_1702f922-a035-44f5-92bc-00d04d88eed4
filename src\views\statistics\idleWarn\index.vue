<template>
  <div class="idle-warn-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">资产空置闲置统计预警</h1>
    </div>

    <!-- 搜索与筛选区 -->
    <a-card :bordered="false" class="search-area">
      <BasicForm @register="registerForm" @submit="handleSearch" />
    </a-card>

    <!-- 操作按钮区 -->
    <div class="button-area">
      <a-button type="primary" @click="handleExport">
        <template #icon><DownloadOutlined /></template>
        导出
      </a-button>
    </div>

    <!-- 数据展示区 -->
    <div class="data-grid">
      <a-card 
        v-for="(item, index) in currentIdleData" 
        :key="index" 
        :bordered="false" 
        class="data-card" 
        :body-style="{ padding: '20px' }"
      >
        <template #title>
          <div class="card-title">
            <component 
              :is="getAssetTypeIcon(item.assetType)" 
              :style="{ marginRight: '8px', color: getAssetTypeColor(item.assetType) }" 
            />
            {{ item.assetType }}
            <a-tag 
              :color="getPropertyRightTagColor(item.propertyRight)" 
              style="margin-left: 10px" 
              size="small"
            >
              {{ item.propertyRightText }}
            </a-tag>
          </div>
        </template>

        <!-- 资产总面积展示 -->
        <div class="total-area-display">
          <div class="total-area-item">
            <div class="total-area-label">资产总面积</div>
            <div class="total-area-value">{{ formatArea(item.totalArea) }} ㎡</div>
          </div>
        </div>

        <!-- 闲置情况 -->
        <a-divider orientation="left">
          <small><ExclamationCircleOutlined style="margin-right: 5px" />闲置情况</small>
        </a-divider>

        <!-- 闲置6个月以上面积 -->
        <div class="data-item">
          <div class="data-item-label">闲置6个月以上面积</div>
          <div 
            class="data-item-value" 
            :class="getValueClass(item.idleOver6MonthsArea, item.totalArea, 0.1)"
          >
            {{ formatArea(item.idleOver6MonthsArea) }} ㎡
          </div>
        </div>

        <!-- 闲置率（6个月以上） -->
        <div class="data-item">
          <div class="data-item-label">闲置率（6个月以上）</div>
          <div 
            class="data-item-value" 
            :class="getValueClass(item.idleRateOver6Months, 100, 10)"
          >
            {{ formatPercentage(item.idleRateOver6Months) }}
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-wrap">
          <div 
            class="progress-bar" 
            :class="getProgressBarClass(item.idleRateOver6Months)"
            :style="{width: Math.min(item.idleRateOver6Months, 100) + '%'}"
          ></div>
        </div>
        <div class="progress-info">
          <span>0%</span>
          <span>50%</span>
          <span>100%</span>
        </div>

        <!-- 闲置资产数量（6个月以上） -->
        <div class="data-item">
          <div class="data-item-label">闲置资产数量（6个月以上）</div>
          <div class="data-item-value">{{ item.idleAssetsCountOver6Months }} 处</div>
        </div>

        <!-- 闲置时间分布 -->
        <a-divider orientation="left">
          <small><ClockCircleOutlined style="margin-right: 5px" />闲置时间分布</small>
        </a-divider>

        <!-- 空置6个月以内面积 -->
        <div class="data-item">
          <div class="data-item-label">空置6个月以内面积</div>
          <div class="data-item-value">{{ formatArea(item.idleUnder6MonthsArea) }} ㎡</div>
        </div>

        <!-- 空置率（6个月以内） -->
        <div class="data-item">
          <div class="data-item-label">空置率（6个月以内）</div>
          <div class="data-item-value">{{ formatPercentage(item.idleRateUnder6Months) }}</div>
        </div>

        <!-- 闲置时间段分布 -->
        <div class="data-item">
          <div class="data-item-label">闲置6至12个月内</div>
          <div class="data-item-value">{{ formatArea(item.idle6To12MonthsArea) }} ㎡</div>
        </div>

        <div class="data-item">
          <div class="data-item-label">闲置1至3年内</div>
          <div class="data-item-value">{{ formatArea(item.idle1To3YearsArea) }} ㎡</div>
        </div>

        <div class="data-item">
          <div class="data-item-label">闲置3年及以上</div>
          <div 
            class="data-item-value" 
            :class="{'danger-value': item.idleOver3YearsArea > 0}"
          >
            {{ formatArea(item.idleOver3YearsArea) }} ㎡
          </div>
        </div>

        <!-- 其他指标 -->
        <a-divider orientation="left">
          <small><BarChartOutlined style="margin-right: 5px" />其他指标</small>
        </a-divider>

        <!-- 闲置率（不含代管） -->
        <div class="data-item">
          <div class="data-item-label">闲置率（不含代管）</div>
          <div 
            class="data-item-value" 
            :class="getValueClass(item.idleRateExcludeAgent, 100, 10)"
          >
            {{ formatPercentage(item.idleRateExcludeAgent) }}
          </div>
        </div>
      </a-card>
    </div>

    <!-- 闲置状态提示弹窗 -->
    <a-modal
      v-model:visible="tooltipVisible"
      title="提示"
      :footer="null"
      width="400px"
    >
      <p>是否闲置为是：查询闲置结束日期为空或者闲置日期在当前日期之后的数据</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    DownloadOutlined,
    ExclamationCircleOutlined,
    ClockCircleOutlined,
    BarChartOutlined,
    HomeOutlined,
    BuildOutlined,
  } from '@ant-design/icons-vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { getIdleWarnList, exportIdleWarn, type IdleWarnItem } from './idleWarn.api';
  import { searchFormSchema } from './idleWarn.data';

  // 注册BasicForm
  const [registerForm] = useForm({
    schemas: searchFormSchema,
    layout: 'inline',
    showActionButtonGroup: true,
    submitButtonOptions: {
      text: '查询',
      preIcon: 'ant-design:search-outlined',
    },
    resetButtonOptions: {
      text: '重置',
      preIcon: 'ant-design:reload-outlined',
    },
    actionColOptions: {
      span: 3,
    },
    baseColProps: {
      style: { paddingRight: '16px' },
    },
  });

  // 空置闲置数据
  const idleData = ref<IdleWarnItem[]>([]);
  const filteredIdleData = ref<IdleWarnItem[]>([]);
  const tooltipVisible = ref(false);

  // 计算当前展示的数据
  const currentIdleData = computed(() => {
    return filteredIdleData.value.length > 0 ? filteredIdleData.value : idleData.value;
  });

  // 处理查询
  const handleSearch = (values: any) => {
    filteredIdleData.value = idleData.value.filter((item) => {
      // 资产类型筛选
      if (values.assetType) {
        const assetTypeMap = {
          'land': '土地',
          'house': '房屋',
        };

        if (assetTypeMap[values.assetType] !== item.assetType) {
          return false;
        }
      }

      // 产权状况筛选
      if (values.propertyRight && values.propertyRight !== item.propertyRight) {
        return false;
      }

      // 是否闲置筛选
      if (values.isIdle === 'yes' && item.idleOver6MonthsArea === 0) {
        return false;
      } else if (values.isIdle === 'no' && item.idleOver6MonthsArea > 0) {
        return false;
      }

      // 所属企业筛选
      const matchesCompanies = !values.companies?.length ||
        (item.companyName && values.companies.includes(item.companyName));
      if (!matchesCompanies) {
        return false;
      }

      return true;
    });
  };

  // 处理导出
  const handleExport = async () => {
    try {
      await exportIdleWarn();
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 格式化面积数字
  const formatArea = (value: number) => {
    return value.toLocaleString('zh-CN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  };

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return value.toFixed(2) + '%';
  };

  // 获取资产类型图标
  const getAssetTypeIcon = (type: string) => {
    const iconMap = {
      '土地': HomeOutlined,
      '房屋': BuildOutlined,
    };
    return iconMap[type] || HomeOutlined;
  };

  // 获取资产类型颜色
  const getAssetTypeColor = (type: string) => {
    const colorMap = {
      '土地': '#52c41a',
      '房屋': '#1890ff',
    };
    return colorMap[type] || '#666';
  };

  // 获取产权标签颜色
  const getPropertyRightTagColor = (propertyRight: string) => {
    const colorMap = {
      yes: 'success',
      no: 'default',
      agent: 'warning',
    };
    return colorMap[propertyRight] || 'default';
  };

  // 获取数值显示样式类
  const getValueClass = (value: number, total: number, threshold: number) => {
    if (!total) return '';

    const percentage = (value / total) * 100;
    if (percentage >= threshold * 2) return 'danger-value';
    if (percentage >= threshold) return 'warning-value';
    return '';
  };

  // 获取进度条样式类
  const getProgressBarClass = (percentage: number) => {
    if (percentage >= 20) return 'danger';
    if (percentage >= 10) return 'warning';
    return '';
  };

  // 加载数据
  const loadData = async () => {
    try {
      const response = await getIdleWarnList();
      idleData.value = response;
    } catch (error) {
      message.error('数据加载失败');
    }
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .idle-warn-container {
    padding: 20px;
    background-color: #f0f2f5;
    min-height: 100vh;

    .page-header {
      margin-bottom: 20px;
      padding: 0 0 16px 0;
      border-bottom: 1px solid #d9d9d9;

      .page-title {
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        margin: 0;
      }
    }

    .search-area {
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .button-area {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;
    }

    // 数据卡片网格布局
    .data-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      width: 100%;
    }

    // 数据卡片样式
    .data-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    // 资产总面积展示
    .total-area-display {
      margin-bottom: 16px;
    }

    .total-area-item {
      text-align: center;
      padding: 12px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .total-area-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .total-area-value {
        font-size: 18px;
        font-weight: bold;
        color: #1890ff;
      }
    }

    // 数据项样式
    .data-item {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .data-item-label {
        color: #666;
        font-size: 13px;
        flex: 1;
      }

      .data-item-value {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        text-align: right;

        &.highlight-value {
          color: #1890ff;
        }

        &.warning-value {
          color: #faad14;
        }

        &.danger-value {
          color: #f5222d;
        }
      }
    }

    // 进度条样式
    .progress-wrap {
      margin-top: 5px;
      margin-bottom: 8px;
      width: 100%;
      height: 6px;
      background-color: #f5f5f5;
      border-radius: 3px;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        background-color: #1890ff;
        border-radius: 3px;
        transition: width 0.3s;

        &.warning {
          background-color: #faad14;
        }

        &.danger {
          background-color: #f5222d;
        }
      }
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
      margin-bottom: 12px;
    }

    // 响应式布局
    @media (max-width: 1400px) {
      .data-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .data-grid {
        grid-template-columns: 1fr;
      }

      .button-area {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
      }
    }
  }
</style>
