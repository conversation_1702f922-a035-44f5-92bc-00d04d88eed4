<template>
  <div class="rent-preview-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">租金一览表</h1>
    </div>

    <!-- 搜索与筛选区 -->
    <a-card :bordered="false" class="search-area">
      <BasicForm @register="registerForm" @submit="handleSearch" />
    </a-card>

    <!-- 操作按钮区 -->
    <div class="button-area">
      <a-button type="primary" @click="handleExport">
        <template #icon><DownloadOutlined /></template>
        导出
      </a-button>
    </div>

    <!-- 数据展示区 -->
    <div v-for="year in sortedYears" :key="year" class="year-group">
      <div class="year-title">
        <CalendarOutlined style="margin-right: 8px; color: #1890ff" />{{ year }}年度租金数据
      </div>
      <div class="data-grid">
        <a-card v-for="(item, index) in groupedRentalData[year]" :key="index" :bordered="false" class="data-card" :body-style="{ padding: '20px' }">
          <template #title>
            <div class="card-title">
              <component :is="getAssetTypeIcon(item.assetType)" :style="{ marginRight: '8px', color: getAssetTypeColor(item.assetType) }" />
              {{ item.assetType }}
              <a-tag v-if="item.quarter" color="blue" style="margin-left: 10px" size="small">
                {{ item.quarter }}
              </a-tag>
              <a-tag v-if="item.propertyRightText" :color="getPropertyRightTagColor(item.propertyRight)" style="margin-left: 10px" size="small">
                {{ item.propertyRightText }}
              </a-tag>
            </div>
          </template>

          <!-- 租金金额展示 -->
          <div class="rent-amount-display">
            <div class="rent-amount-item">
              <div class="rent-amount-label">应收租金</div>
              <div class="rent-amount-value rent-expected">{{ formatAmount(item.expectedRental) }}</div>
            </div>
            <div class="rent-amount-item">
              <div class="rent-amount-label">实收租金</div>
              <div class="rent-amount-value rent-paid">{{ formatAmount(item.paidRental) }}</div>
            </div>
            <div class="rent-amount-item">
              <div class="rent-amount-label">未收租金</div>
              <div class="rent-amount-value rent-unpaid">{{ formatAmount(item.unpaidRental) }}</div>
            </div>
          </div>

          <!-- 数据项网格 -->
          <div class="data-items-grid">
            <!-- 分类1 - 招商类型 -->
            <div class="category-title"> <TagsOutlined style="margin-right: 5px" />分类1 - 招商类型 </div>

            <div class="vertical-data-item">
              <div class="vertical-data-label">专业化招商实收租金</div>
              <div class="vertical-data-value highlight-value">{{ formatAmount(item.professionalRental) }}</div>
            </div>

            <div class="vertical-data-item">
              <div class="vertical-data-label">非专业化招商实收租金</div>
              <div class="vertical-data-value">{{ formatAmount(item.nonProfessionalRental) }}</div>
            </div>

            <!-- 分类2 - 招租方式 -->
            <div class="category-title"> <AppstoreOutlined style="margin-right: 5px" />分类2 - 招租方式 </div>

            <div class="vertical-data-item">
              <div class="vertical-data-label">厦门公开招租(进场)实收租金</div>
              <div class="vertical-data-value">{{ formatAmount(item.xiamenPublicRental) }}</div>
            </div>

            <div class="vertical-data-item">
              <div class="vertical-data-label">异地公开招租(进场)实收租金</div>
              <div class="vertical-data-value">{{ formatAmount(item.otherPlacePublicRental) }}</div>
            </div>
            
            <div class="vertical-data-item">
              <div class="vertical-data-label">公开招租(非进场)实收租金</div>
              <div class="vertical-data-value">{{ formatAmount(item.nonEntryPublicRental) }}</div>
            </div>
            
            <div class="vertical-data-item">
              <div class="vertical-data-label">其他方式招租实收租金</div>
              <div class="vertical-data-value">{{ formatAmount(item.otherMethodRental) }}</div>
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    DownloadOutlined,
    CalendarOutlined,
    TagsOutlined,
    AppstoreOutlined,
    HomeOutlined,
    BuildOutlined,
    ShopOutlined,
    SettingOutlined,
    AppstoreAddOutlined
  } from '@ant-design/icons-vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import { getRentPreviewList, exportRentPreview, type RentPreviewItem } from './rentPreview.api';
  import { searchFormSchema } from './rentPreview.data';

  // 注册BasicForm
  const [registerForm] = useForm({
    schemas: searchFormSchema,
    layout: 'inline',
    showActionButtonGroup: true,
    submitButtonOptions: {
      text: '查询',
      preIcon: 'ant-design:search-outlined',
    },
    resetButtonOptions: {
      text: '重置',
      preIcon: 'ant-design:reload-outlined',
    },
    actionColOptions: {
      span: 4,
    },
    baseColProps: {
      style: { paddingRight: '16px' },
    },
  });

  // 租金数据
  const rentalData = ref<RentPreviewItem[]>([]);
  const filteredRentalData = ref<RentPreviewItem[]>([]);

  // 计算当前展示的数据
  const currentRentalData = computed(() => {
    return filteredRentalData.value.length > 0 ? filteredRentalData.value : rentalData.value;
  });

  // 按年份分组的数据
  const groupedRentalData = computed(() => {
    const data = currentRentalData.value;
    const grouped = {};
    
    data.forEach(item => {
      if (!grouped[item.year]) {
        grouped[item.year] = [];
      }
      grouped[item.year].push(item);
    });
    
    // 对每个年份的数据按季度和资产类型排序
    Object.keys(grouped).forEach(year => {
      grouped[year].sort((a, b) => {
        // 首先按季度排序
        const quarterOrder = {
          '一季度': 1,
          '二季度': 2,
          '三季度': 3,
          '四季度': 4
        };
        
        const quarterDiff = quarterOrder[a.quarter] - quarterOrder[b.quarter];
        
        // 如果季度相同，则按资产类型排序
        if (quarterDiff === 0) {
          const typeOrder = {
            '土地': 1,
            '房屋': 2,
            '广告位': 3,
            '设备': 4,
            '其他': 5
          };
          
          return typeOrder[a.assetType] - typeOrder[b.assetType];
        }
        
        return quarterDiff;
      });
    });
    
    return grouped;
  });

  // 排序后的年份（从近到远）
  const sortedYears = computed(() => {
    return Object.keys(groupedRentalData.value).sort((a, b) => parseInt(b) - parseInt(a));
  });

  // 处理查询
  const handleSearch = (values: any) => {
    filteredRentalData.value = rentalData.value.filter((item) => {
      // 年份范围筛选
      if (values.yearRange && values.yearRange.length === 2) {
        const startYear = parseInt(values.yearRange[0]);
        const endYear = parseInt(values.yearRange[1]);
        const itemYear = parseInt(item.year);
        if (itemYear < startYear || itemYear > endYear) {
          return false;
        }
      }
      
      // 季度筛选
      if (values.quarters && values.quarters.length > 0) {
        const quarterMap = {
          'Q1': '一季度',
          'Q2': '二季度',
          'Q3': '三季度',
          'Q4': '四季度'
        };
        
        const selectedQuarters = values.quarters.map(q => quarterMap[q]);
        
        if (!selectedQuarters.includes(item.quarter)) {
          return false;
        }
      }
      
      // 资产类型筛选
      if (values.assetTypes && values.assetTypes.length > 0) {
        const assetTypeMap = {
          'land': '土地',
          'house': '房屋',
          'ad': '广告位',
          'equipment': '设备',
          'other': '其他'
        };
        
        const matchingTypes = values.assetTypes.map(t => assetTypeMap[t]);
        
        if (!matchingTypes.includes(item.assetType)) {
          return false;
        }
      }
      
      // 产权状况筛选
      if (values.propertyRights && values.propertyRights.length > 0) {
        if (!values.propertyRights.includes(item.propertyRight)) {
          return false;
        }
      }
      
      // 所属企业筛选
      const matchesCompanies = !values.companies?.length || 
        (item.companyName && values.companies.includes(item.companyName));
      if (!matchesCompanies) {
        return false;
      }
      
      return true;
    });
  };



  // 处理导出
  const handleExport = async () => {
    try {
      await exportRentPreview();
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    return amount ? amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' 万元' : '0.00 万元';
  };

  // 获取资产类型图标
  const getAssetTypeIcon = (type: string) => {
    const iconMap = {
      '土地': HomeOutlined,
      '房屋': BuildOutlined,
      '广告位': ShopOutlined,
      '设备': SettingOutlined,
      '其他': AppstoreAddOutlined
    };
    return iconMap[type] || AppstoreAddOutlined;
  };

  // 获取资产类型颜色
  const getAssetTypeColor = (type: string) => {
    const colorMap = {
      '土地': '#52c41a',
      '房屋': '#1890ff',
      '广告位': '#faad14',
      '设备': '#13c2c2',
      '其他': '#722ed1'
    };
    return colorMap[type] || '#666';
  };

  // 获取产权标签颜色
  const getPropertyRightTagColor = (propertyRight: string) => {
    const colorMap = {
      yes: 'success',
      no: 'default',
      agent: 'warning',
    };
    return colorMap[propertyRight] || 'default';
  };

  // 加载数据
  const loadData = async () => {
    try {
      const response = await getRentPreviewList();
      rentalData.value = response;
    } catch (error) {
      message.error('数据加载失败');
    }
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadData();
  });
</script>

<style lang="less" scoped>
  .rent-preview-container {
    padding: 20px;
    background-color: #f0f2f5;
    min-height: 100vh;

    .page-header {
      margin-bottom: 20px;
      padding: 0 0 16px 0;
      border-bottom: 1px solid #d9d9d9;

      .page-title {
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        margin: 0;
      }
    }

    .search-area {
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .button-area {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;
    }

    // 年份分组样式
    .year-group {
      margin-bottom: 30px;

      .year-title {
        font-size: 18px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
      }
    }

    // 数据卡片网格布局
    .data-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;
      width: 100%;
    }

    // 数据卡片样式
    .data-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    // 租金金额展示
    .rent-amount-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      gap: 8px;
    }

    .rent-amount-item {
      text-align: center;
      flex: 1;
      padding: 12px 8px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .rent-amount-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .rent-amount-value {
        font-size: 16px;
        font-weight: bold;

        &.rent-expected {
          color: #1890ff;
        }

        &.rent-paid {
          color: #52c41a;
        }

        &.rent-unpaid {
          color: #f5222d;
        }
      }
    }

    // 数据项网格布局
    .data-items-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px 16px;
    }

    // 分类标题
    .category-title {
      grid-column: 1 / -1;
      margin-top: 16px;
      margin-bottom: 8px;
      font-size: 13px;
      color: #666;
      border-bottom: 1px dashed #eee;
      padding-bottom: 4px;
      display: flex;
      align-items: center;
    }

    // 垂直排列的数据项
    .vertical-data-item {
      background-color: #f9f9f9;
      border-radius: 4px;
      padding: 10px;
      text-align: center;

      .vertical-data-label {
        color: #666;
        font-size: 12px;
        margin-bottom: 5px;
        white-space: normal;
        line-height: 1.3;
      }

      .vertical-data-value {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;

        &.highlight-value {
          color: #1890ff;
        }
      }
    }

    // 响应式布局
    @media (max-width: 1400px) {
      .data-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 992px) {
      .data-items-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .data-grid {
        grid-template-columns: 1fr;
      }

      .button-area {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
      }

      .rent-amount-display {
        flex-direction: column;
        gap: 8px;
      }
    }
  }
</style>
