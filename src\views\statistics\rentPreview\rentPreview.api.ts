import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/rentPreview/list',
  export = '/mock/rentPreview/export',
}

// 租金数据接口类型定义
export interface RentPreviewItem {
  year: string;
  quarter: string;
  assetType: string;
  propertyRight: string;
  propertyRightText: string;
  expectedRental: number;
  paidRental: number;
  unpaidRental: number;
  professionalRental: number;
  nonProfessionalRental: number;
  xiamenPublicRental: number;
  otherPlacePublicRental: number;
  nonEntryPublicRental: number;
  otherMethodRental: number;
  companyName?: string; // 所属企业名称
}

/**
 * 获取租金一览表数据
 * @param params
 */
export const getRentPreviewList = (params?: any): Promise<RentPreviewItem[]> => 
  defHttp.get({ url: Api.list, params });

/**
 * 导出租金一览表数据
 * @param params
 */
export const exportRentPreview = (params?: any) => 
  defHttp.get({ url: Api.export, params, responseType: 'blob' });
