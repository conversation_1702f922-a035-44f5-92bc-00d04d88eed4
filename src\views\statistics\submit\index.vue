<template>
  <div class="report-warn-container">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport">导出</a-button>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="report-warn-list" setup>
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema } from './reportWarn.data';
  import { list, exportReportWarn } from './reportWarn.api';
  import './index.less';

  const { createMessage } = useMessage();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'report-warn-list',
    tableProps: {
      title: '资产报送统计预警列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      showActionColumn: false,
      tableSetting: {
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      // 搜索表单配置
      formConfig: {
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: false,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '50', '100'],
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
      },
    },
  });

  const [registerTable] = tableContext;

  // 导出数据
  function handleExport() {
    exportReportWarn({}).then(() => {
      createMessage.success('数据导出成功！');
    }).catch(() => {
      createMessage.error('导出失败');
    });
  }
</script>
