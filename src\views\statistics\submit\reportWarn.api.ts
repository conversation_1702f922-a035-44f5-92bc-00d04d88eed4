import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/reportWarn/list',
  export = '/mock/reportWarn/export',
  exportAll = '/mock/reportWarn/exportAll',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 导出选中数据
 * @param params
 */
export const exportReportWarn = (params) => defHttp.get({ url: Api.export, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllReportWarn = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' });
