import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '资产信息',
    dataIndex: 'assetInfo',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '资产数量',
    dataIndex: 'assetCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '自用',
    dataIndex: 'selfUse',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '自用数量',
    dataIndex: 'selfUseCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '出租',
    dataIndex: 'rent',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '出租数量',
    dataIndex: 'rentCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '闲置',
    dataIndex: 'idle',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '闲置数量',
    dataIndex: 'idleCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '占用',
    dataIndex: 'occupy',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '占用数量',
    dataIndex: 'occupyCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '借用',
    dataIndex: 'borrow',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '借用数量',
    dataIndex: 'borrowCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
  {
    title: '转让',
    dataIndex: 'transfer',
    width: 180,
    customRender: ({ text }) => {
      // 日期类型，使用primary颜色
      return h('span', { class: 'date-cell' }, text);
    },
  },
  {
    title: '转让数量',
    dataIndex: 'transferCount',
    width: 120,
    customRender: ({ text }) => {
      // 数字类型，使用success颜色
      return h('span', { class: 'number-cell' }, text);
    },
  },
];

export const searchFormSchema: FormSchema[] = [
  // 暂时不需要搜索表单，根据原型文件只有导出和列设置功能
];
