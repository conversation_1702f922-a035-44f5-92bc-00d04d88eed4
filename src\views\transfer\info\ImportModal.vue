<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="'导入转让信息'"
    :width="650"
    :canFullscreen="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :okButtonProps="{ disabled: !canSubmit }"
  >
    <div class="transfer-import-modal">
      <!-- 提示信息 -->
      <a-alert
        message="请先下载导入模板，按照模板格式填写后上传"
        type="info"
        show-icon
        :closable="false"
        class="upload-tip"
      />

      <!-- 下载模板按钮 -->
      <div class="template-download">
        <a-button type="primary" @click="handleDownloadTemplate" :loading="downloadLoading">
          <template #icon>
            <DownloadOutlined />
          </template>
          下载导入模板
        </a-button>
      </div>

      <!-- 文件上传 -->
      <a-upload-dragger
        v-model:fileList="fileList"
        :multiple="false"
        :before-upload="beforeUpload"
        :remove="handleRemove"
        accept=".xlsx,.xls"
        :maxCount="1"
        @change="handleChange"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">将文件拖到此处，或<em>点击上传</em></p>
        <p class="ant-upload-hint">只能上传xlsx/xls文件，且不超过10MB</p>
      </a-upload-dragger>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <a-progress :percent="uploadProgress" />
        <p>正在上传文件...</p>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <a-alert
          :message="importResult.message"
          :type="importResult.success ? 'success' : 'error'"
          show-icon
          :closable="false"
        />
        <div v-if="importResult.details" class="result-details">
          <p>成功导入：{{ importResult.details.successCount }} 条</p>
          <p v-if="importResult.details.failCount > 0">
            失败：{{ importResult.details.failCount }} 条
          </p>
          <div v-if="importResult.details.errors && importResult.details.errors.length > 0">
            <p>错误详情：</p>
            <ul>
              <li v-for="(error, index) in importResult.details.errors" :key="index">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" name="TransferImportModal" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { downloadTemplate, importTransfer } from './transfer.api';
  import { InboxOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();

  // 模态框注册
  const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
    resetForm();
  });

  // 响应式数据
  const fileList = ref<UploadFile[]>([]);
  const uploading = ref(false);
  const uploadProgress = ref(0);
  const downloadLoading = ref(false);
  const importResult = ref<any>(null);

  // 计算属性
  const canSubmit = computed(() => {
    return fileList.value.length > 0 && !uploading.value;
  });

  /**
   * 重置表单
   */
  function resetForm() {
    fileList.value = [];
    uploading.value = false;
    uploadProgress.value = 0;
    importResult.value = null;
  }

  /**
   * 下载模板
   */
  async function handleDownloadTemplate() {
    try {
      downloadLoading.value = true;
      await downloadTemplate();
    } catch (error) {
      createMessage.error('模板下载失败');
    } finally {
      downloadLoading.value = false;
    }
  }

  /**
   * 文件上传前验证
   */
  function beforeUpload(file: UploadFile) {
    const isExcel = file.type === 'application/vnd.ms-excel' || 
                    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    
    if (!isExcel) {
      createMessage.error('只能上传Excel文件！');
      return false;
    }

    const isLt10M = (file.size || 0) / 1024 / 1024 < 10;
    if (!isLt10M) {
      createMessage.error('文件大小不能超过10MB！');
      return false;
    }

    return false; // 阻止自动上传
  }

  /**
   * 文件变化处理
   */
  function handleChange(info: UploadChangeParam) {
    importResult.value = null;
  }

  /**
   * 移除文件
   */
  function handleRemove() {
    importResult.value = null;
    return true;
  }

  /**
   * 提交导入
   */
  async function handleSubmit() {
    if (fileList.value.length === 0) {
      createMessage.warning('请先选择要导入的文件');
      return;
    }

    try {
      uploading.value = true;
      uploadProgress.value = 0;
      setModalProps({ confirmLoading: true });

      const file = fileList.value[0];
      const formData = new FormData();
      formData.append('file', file.originFileObj as File);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadProgress.value < 90) {
          uploadProgress.value += 10;
        }
      }, 200);

      const result = await importTransfer(formData);
      
      clearInterval(progressInterval);
      uploadProgress.value = 100;

      importResult.value = {
        success: true,
        message: '导入成功',
        details: result
      };

      createMessage.success('导入成功');
      emit('success');
      
      // 延迟关闭模态框
      setTimeout(() => {
        closeModal();
      }, 2000);

    } catch (error: any) {
      importResult.value = {
        success: false,
        message: '导入失败',
        details: error.response?.data || error
      };
      createMessage.error('导入失败');
    } finally {
      uploading.value = false;
      setModalProps({ confirmLoading: false });
    }
  }

  /**
   * 取消导入
   */
  function handleCancel() {
    resetForm();
  }
</script>

<style lang="less" scoped>
  .transfer-import-modal {
    .upload-tip {
      margin-bottom: 20px;
    }

    .template-download {
      text-align: center;
      margin-bottom: 24px;
    }

    .upload-progress {
      margin-top: 16px;
      text-align: center;

      p {
        margin-top: 8px;
        color: #666;
      }
    }

    .import-result {
      margin-top: 16px;

      .result-details {
        margin-top: 12px;
        padding: 12px;
        background-color: #f5f5f5;
        border-radius: 4px;

        p {
          margin: 4px 0;
        }

        ul {
          margin: 8px 0 0 16px;
          
          li {
            color: #f5222d;
            margin: 4px 0;
          }
        }
      }
    }
  }
</style>
