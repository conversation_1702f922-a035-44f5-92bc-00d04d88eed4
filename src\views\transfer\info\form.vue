<template>
  <div class="transfer-form">
    <div class="simple-title">转让资产包信息表单</div>
    <div class="p-4">
      <a-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        layout="horizontal"
      >
        <!-- 资产包信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:box-outlined" class="title-icon" />
              资产包信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让资产包编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让资产包名称" name="name">
                  <a-input v-model:value="formData.name" placeholder="请输入转让资产包名称" />
                  <div class="help-text">系统内要求资产名称唯一</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让方式" name="type">
                  <a-select
                    v-model:value="formData.type"
                    placeholder="请选择转让方式"
                    @change="handleTransferTypeChange"
                  >
                    <a-select-option :value="0">厦门公开转让(进场)</a-select-option>
                    <a-select-option :value="1">异地公开转让(进场)</a-select-option>
                    <a-select-option :value="2">公开转让(非进场)</a-select-option>
                    <a-select-option :value="3">其他转让</a-select-option>
                    <a-select-option :value="4">其他方式转让(协议)</a-select-option>
                    <a-select-option :value="5">无偿划转</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="主要资产类型" name="assetsType">
                  <a-select v-model:value="formData.assetsType" placeholder="请选择主要资产类型">
                    <a-select-option :value="0">土地</a-select-option>
                    <a-select-option :value="1">房屋</a-select-option>
                    <a-select-option :value="2">设备</a-select-option>
                    <a-select-option :value="3">广告位</a-select-option>
                    <a-select-option :value="4">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产所在地区" name="assetsLocation">
                  <JAreaLinkage
                    v-model:value="formData.assetsLocation"
                    placeholder="请选择省份/城市/区县"
                    :showArea="true"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产来源" name="source">
                  <a-select v-model:value="formData.source" placeholder="请选择资产来源">
                    <a-select-option :value="0">企业实物资产</a-select-option>
                    <a-select-option :value="1">行政事业单位实物资产</a-select-option>
                    <a-select-option :value="2">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit">
                  <a-select v-model:value="formData.manageUnit" placeholder="请选择管理单位">
                    <a-select-option
                      v-for="item in enterpriseOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot">
                  <a-select v-model:value="formData.reportOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk">
                  <a-input v-model:value="formData.entryClerk" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime">
                  <a-input v-model:value="formData.createTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" name="status">
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option
                      v-for="item in statusOptions"
                      :key="item.value"
                      :value="item.value"
                      :disabled="item.disabled"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <div class="help-text">备案数据支持撤回、草稿数据和撤回数据支持作废</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="备注" name="remark">
                  <a-input v-model:value="formData.remark" placeholder="请输入备注" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 挂牌信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:tag-outlined" class="title-icon" />
              挂牌信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item
                  label="挂牌机构"
                  name="listingOrganization"
                  :rules="formData.type === 1 ? [{ required: true, message: '请输入挂牌机构' }] : []"
                >
                  <a-input
                    v-model:value="formData.listingOrganization"
                    placeholder="请输入挂牌机构"
                    :disabled="formData.type !== 1"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  label="挂牌机构-所在地区"
                  name="listingLocation"
                  :rules="formData.type === 1 ? [{ required: true, message: '请选择挂牌机构所在地区' }] : []"
                >
                  <JAreaLinkage
                    v-model:value="formData.listingLocation"
                    placeholder="请选择省份/城市"
                    :disabled="formData.type !== 1"
                    :showArea="false"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 转让方信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:building-outlined" class="title-icon" />
              转让方信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让方名称" name="sellName">
                  <a-select
                    v-model:value="formData.sellName"
                    placeholder="请选择转让方名称"
                    filterable
                    @change="handleSellNameChange"
                  >
                    <a-select-option
                      v-for="item in sellCompanyOptions"
                      :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让方类型" name="sellType">
                  <a-select v-model:value="formData.sellType" placeholder="请选择转让方类型">
                    <a-select-option :value="0">法人单位</a-select-option>
                    <a-select-option :value="1">非法人单位</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="是否联合转让" name="jointSell">
                  <a-radio-group v-model:value="formData.jointSell">
                    <a-radio :value="0">否</a-radio>
                    <a-radio :value="1">是</a-radio>
                  </a-radio-group>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="转让方所在地区" name="sellLocation">
                  <JAreaLinkage
                    v-model:value="formData.sellLocation"
                    placeholder="请选择转让方所在地区"
                    :showArea="true"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="注册地地址" name="registration">
                  <a-input v-model:value="formData.registration" placeholder="请输入注册地地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="注册资本（万元）" name="registeredCapital">
                  <a-input-number
                    v-model:value="formData.registeredCapital"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                    placeholder="请输入注册资本"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="企业类型" name="enterpriseType">
                  <a-select v-model:value="formData.enterpriseType" placeholder="请选择企业类型">
                    <a-select-option :value="0">全民所有制企业</a-select-option>
                    <a-select-option :value="1">有限责任公司</a-select-option>
                    <a-select-option :value="2">股份有限公司</a-select-option>
                    <a-select-option :value="3">集体所有制</a-select-option>
                    <a-select-option :value="4">合伙企业</a-select-option>
                    <a-select-option :value="5">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经济类型" name="economicType">
                  <a-select v-model:value="formData.economicType" placeholder="请选择经济类型">
                    <a-select-option :value="0">国有独资企业</a-select-option>
                    <a-select-option :value="1">国有全资企业</a-select-option>
                    <a-select-option :value="2">国有控股企业</a-select-option>
                    <a-select-option :value="3">国有实际控制企业</a-select-option>
                    <a-select-option :value="4">国有参股企业</a-select-option>
                    <a-select-option :value="5">事业单位</a-select-option>
                    <a-select-option :value="6">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="法定代表人" name="legalRepresentative">
                  <a-input v-model:value="formData.legalRepresentative" placeholder="请输入法定代表人" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="经营规模" name="scale">
                  <a-select v-model:value="formData.scale" placeholder="请选择经营规模">
                    <a-select-option :value="0">大型</a-select-option>
                    <a-select-option :value="1">中型</a-select-option>
                    <a-select-option :value="2">小型</a-select-option>
                    <a-select-option :value="3">微型</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="统一社会信用代码或组织机构代码" name="orgCode">
                  <a-input v-model:value="formData.orgCode" placeholder="请输入统一社会信用代码或组织机构代码" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="内部决策情况" name="decisionMaking">
                  <a-select v-model:value="formData.decisionMaking" placeholder="请选择内部决策情况">
                    <a-select-option :value="0">董事会决议</a-select-option>
                    <a-select-option :value="1">股东会决议</a-select-option>
                    <a-select-option :value="2">总经理办公会决议</a-select-option>
                    <a-select-option :value="3">其他类型</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="附件" name="sellFiles" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <JUpload
                    v-model:value="formData.sellFiles"
                    :fileType="UploadTypeEnum.ALL"
                    :maxCount="10"
                    :maxSize="50"
                    :returnUrl="false"
                    text="点击上传"
                    helpText="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产评估核准或备案情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:clipboard-check-outlined" class="title-icon" />
              资产评估核准或备案情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="评估机构" name="evaluateOrg">
                  <a-input v-model:value="formData.evaluateOrg" placeholder="请输入评估机构" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估核准（备案）机构" name="approvalOrg">
                  <a-input v-model:value="formData.approvalOrg" placeholder="请输入评估核准（备案）机构" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="核准（备案）日期" name="approvalDate">
                  <a-date-picker
                    v-model:value="formData.approvalDate"
                    placeholder="请选择核准（备案）日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="评估基准日" name="evaluateDate">
                  <a-input v-model:value="formData.evaluateDate" placeholder="请输入评估基准日" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估报告文号" name="evaluateReport">
                  <a-input v-model:value="formData.evaluateReport" placeholder="请输入评估报告文号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="评估基准日审计机构" name="evaluateDateOrg">
                  <a-input v-model:value="formData.evaluateDateOrg" placeholder="请输入评估基准日审计机构" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="律师事务所" name="lawFirm">
                  <a-input v-model:value="formData.lawFirm" placeholder="请输入律师事务所" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="标的评估值（万元）" name="targetPrice">
                  <a-input v-model:value="formData.targetPrice" placeholder="请输入标的评估值" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="转让标的评估总值（万元）" name="sellTargetPrice">
                  <a-input v-model:value="formData.sellTargetPrice" placeholder="请输入转让标的评估总值" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="账面原值（万元）" name="bookAmount">
                  <a-input v-model:value="formData.bookAmount" placeholder="请输入账面原值" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面净值（万元）" name="bookNetAmount">
                  <a-input v-model:value="formData.bookNetAmount" placeholder="请输入账面净值" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 披露信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              披露信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item
                  label="挂牌（公示）开始时间"
                  name="listingStartDate"
                  :rules="[1, 2].includes(formData.type) ? [{ required: true, message: '请选择挂牌（公示）开始时间' }] : []"
                >
                  <a-date-picker
                    v-model:value="formData.listingStartDate"
                    placeholder="请选择挂牌（公示）开始时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="挂牌（公示）截止时间"
                  name="listingEndDate"
                  :rules="[1, 2].includes(formData.type) ? [{ required: true, message: '请选择挂牌（公示）截止时间' }] : []"
                >
                  <a-date-picker
                    v-model:value="formData.listingEndDate"
                    placeholder="请选择挂牌（公示）截止时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item
                  label="内容描述"
                  name="contentDescription"
                  :label-col="{ span: 3 }"
                  :wrapper-col="{ span: 21 }"
                  :rules="[{ required: true, message: '请输入内容描述' }]"
                >
                  <a-textarea
                    v-model:value="formData.contentDescription"
                    :rows="4"
                    placeholder="请输入内容描述"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="附件" name="disclosureFiles" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <JUpload
                    v-model:value="formData.disclosureFiles"
                    :fileType="UploadTypeEnum.ALL"
                    :maxCount="10"
                    :maxSize="50"
                    :returnUrl="false"
                    text="点击上传"
                    helpText="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 关联资产 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:link-outlined" class="title-icon" />
              关联资产
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addAssociatedAsset">
                <Icon icon="ant-design:plus-outlined" />
                添加关联资产
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <div v-if="formData.associatedAssets.length > 0">
              <a-table
                :dataSource="formData.associatedAssets"
                :columns="associatedAssetsColumns"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 1200 }"
              >
                <template #bodyCell="{ column, record, index }">
                  <!-- 资产类型列 -->
                  <template v-if="column.dataIndex === 'assetType'">
                    <a-form-item
                      :name="['associatedAssets', index, 'assetType']"
                      :rules="[{ required: true, message: '请选择资产类型' }]"
                      class="mb-0"
                    >
                      <a-select
                        v-model:value="record.assetType"
                        placeholder="请选择资产类型"
                        size="small"
                        @change="() => handleAssetTypeChange(record, index)"
                      >
                        <a-select-option :value="0">土地</a-select-option>
                        <a-select-option :value="1">房屋</a-select-option>
                        <a-select-option :value="2">设备</a-select-option>
                        <a-select-option :value="3">广告位</a-select-option>
                        <a-select-option :value="4">其他</a-select-option>
                      </a-select>
                    </a-form-item>
                  </template>

                  <!-- 资产名称（资产编号）列 -->
                  <template v-else-if="column.dataIndex === 'assetsCode'">
                    <a-form-item
                      :name="['associatedAssets', index, 'assetsCode']"
                      :rules="[{ required: true, message: '请选择资产名称（资产编号）' }]"
                      class="mb-0"
                    >
                      <a-select
                        v-model:value="record.assetsCode"
                        mode="multiple"
                        placeholder="请选择资产名称（资产编号）"
                        size="small"
                        :options="assetOptions"
                        :filter-option="filterAssetOption"
                        @change="(value) => handleAssetsCodeChange(record, value, index)"
                      />
                    </a-form-item>
                  </template>

                  <!-- 标的名称列 -->
                  <template v-else-if="column.dataIndex === 'targetName'">
                    <a-form-item
                      :name="['associatedAssets', index, 'targetName']"
                      :rules="[{ required: true, message: '请输入标的名称' }]"
                      class="mb-0"
                    >
                      <a-input v-model:value="record.targetName" placeholder="请输入标的名称" size="small" />
                    </a-form-item>
                  </template>

                  <!-- 挂牌价格列 -->
                  <template v-else-if="column.dataIndex === 'listingPrice'">
                    <a-form-item
                      :name="['associatedAssets', index, 'listingPrice']"
                      :rules="formData.type === 1 ? [{ required: true, message: '请输入挂牌价格' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.listingPrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        size="small"
                        style="width: 100%"
                        placeholder="请输入挂牌价格"
                      />
                    </a-form-item>
                  </template>

                  <!-- 评估价格列 -->
                  <template v-else-if="column.dataIndex === 'evaluatePrice'">
                    <a-form-item
                      :name="['associatedAssets', index, 'evaluatePrice']"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.evaluatePrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        size="small"
                        style="width: 100%"
                        placeholder="请输入评估价格"
                      />
                    </a-form-item>
                  </template>

                  <!-- 转让面积列 -->
                  <template v-else-if="column.dataIndex === 'sellArea'">
                    <a-form-item
                      :name="['associatedAssets', index, 'sellArea']"
                      :rules="[0, 1].includes(record.assetType) ? [{ required: true, message: '请输入转让面积' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.sellArea"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        size="small"
                        style="width: 100%"
                        placeholder="请输入转让面积"
                      />
                    </a-form-item>
                  </template>

                  <!-- 账面净值列 -->
                  <template v-else-if="column.dataIndex === 'bookNetAmount'">
                    <a-form-item
                      :name="['associatedAssets', index, 'bookNetAmount']"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.bookNetAmount"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        size="small"
                        style="width: 100%"
                        placeholder="请输入账面净值"
                      />
                    </a-form-item>
                  </template>

                  <!-- 操作列 -->
                  <template v-else-if="column.dataIndex === 'action'">
                    <a-button
                      type="primary"
                      danger
                      size="small"
                      shape="circle"
                      @click="removeAssociatedAsset(index)"
                    >
                      <Icon icon="ant-design:delete-outlined" />
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-if="formData.associatedAssets.length === 0" class="empty-hint" style="padding: 20px; text-align: center; color: #999;">
              暂无关联资产，请点击上方"添加关联资产"按钮添加。
            </div>
          </div>
        </div>

        <!-- 成交信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:handshake-outlined" class="title-icon" />
              成交信息
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addTransaction">
                <Icon icon="ant-design:plus-outlined" />
                添加成交信息
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <div v-if="formData.transactions.length > 0">
              <a-table
                :dataSource="formData.transactions"
                :columns="transactionsColumns"
                :pagination="false"
                bordered
                size="small"
                :scroll="{ x: 1000 }"
              >
                <template #bodyCell="{ column, record, index }">
                  <!-- 标的名称列 -->
                  <template v-if="column.dataIndex === 'targetName'">
                    <a-form-item
                      :name="['transactions', index, 'targetName']"
                      :rules="[{ required: true, message: '请选择标的名称' }]"
                      class="mb-0"
                    >
                      <a-select
                        v-model:value="record.targetName"
                        placeholder="请选择标的名称"
                        size="small"
                        filterable
                      >
                        <a-select-option
                          v-for="asset in formData.associatedAssets"
                          :key="asset.targetName"
                          :value="asset.targetName"
                        >
                          {{ asset.targetName }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>
                  </template>

                  <!-- 是否成交列 -->
                  <template v-else-if="column.dataIndex === 'dealStatus'">
                    <a-form-item
                      :name="['transactions', index, 'dealStatus']"
                      :rules="[{ required: true, message: '请选择是否成交' }]"
                      class="mb-0"
                    >
                      <a-radio-group
                        v-model:value="record.dealStatus"
                        size="small"
                        @change="() => handleDealStatusChange(record, index)"
                        style="white-space: nowrap;"
                      >
                        <a-radio :value="0" style="margin-right: 8px;">否</a-radio>
                        <a-radio :value="1">是</a-radio>
                      </a-radio-group>
                    </a-form-item>
                  </template>

                  <!-- 受让方列 -->
                  <template v-else-if="column.dataIndex === 'transferee'">
                    <a-form-item
                      :name="['transactions', index, 'transferee']"
                      :rules="record.dealStatus === 1 ? [{ required: true, message: '请输入受让方' }] : []"
                      class="mb-0"
                    >
                      <a-input
                        v-model:value="record.transferee"
                        placeholder="请输入受让方"
                        size="small"
                        :disabled="record.dealStatus !== 1"
                      />
                    </a-form-item>
                  </template>

                  <!-- 成交价格列 -->
                  <template v-else-if="column.dataIndex === 'dealPrice'">
                    <a-form-item
                      :name="['transactions', index, 'dealPrice']"
                      :rules="record.dealStatus === 1 ? [{ required: true, message: '请输入成交价格' }] : []"
                      class="mb-0"
                    >
                      <a-input-number
                        v-model:value="record.dealPrice"
                        :precision="2"
                        :min="0"
                        :controls="false"
                        size="small"
                        style="width: 100%"
                        placeholder="请输入成交价格"
                        :disabled="record.dealStatus !== 1"
                      />
                    </a-form-item>
                  </template>

                  <!-- 成交面积列 -->
                  <template v-else-if="column.dataIndex === 'dealArea'">
                    <a-form-item
                      :name="['transactions', index, 'dealArea']"
                      :rules="record.dealStatus === 1 ? [{ required: true, message: '请输入成交面积' }] : []"
                      class="mb-0"
                    >
                      <a-input
                        v-model:value="record.dealArea"
                        placeholder="请输入成交面积"
                        size="small"
                        :disabled="record.dealStatus !== 1"
                      />
                    </a-form-item>
                  </template>

                  <!-- 成交日期列 -->
                  <template v-else-if="column.dataIndex === 'dealDate'">
                    <a-form-item
                      :name="['transactions', index, 'dealDate']"
                      :rules="record.dealStatus === 1 ? [{ required: true, message: '请选择成交日期' }] : []"
                      class="mb-0"
                    >
                      <a-date-picker
                        v-model:value="record.dealDate"
                        placeholder="请选择成交日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        size="small"
                        style="width: 100%"
                        :disabled="record.dealStatus !== 1"
                      />
                    </a-form-item>
                  </template>

                  <!-- 操作列 -->
                  <template v-else-if="column.dataIndex === 'action'">
                    <a-button
                      type="primary"
                      danger
                      size="small"
                      shape="circle"
                      @click="removeTransaction(index)"
                    >
                      <Icon icon="ant-design:delete-outlined" />
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-if="formData.transactions.length === 0" class="empty-hint" style="padding: 20px; text-align: center; color: #999;">
              暂无成交信息，请点击上方"添加成交信息"按钮添加。
            </div>
          </div>
        </div>

        <!-- 表单提交 -->
        <div class="form-footer">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" :loading="loading" @click="submitForm" style="margin-left: 12px">{{ submitButtonText }}</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { saveOrUpdate, getDetail } from './transfer.api';
import { JAreaLinkage } from '/@/components/Form';
import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
import { UploadTypeEnum } from '/@/components/Form/src/jeecg/components/JUpload/upload.data';

const route = useRoute();
const router = useRouter();

// 表单引用
const formRef = ref();

// 页面状态
const isEditMode = ref(false);
const submitButtonText = ref('提交');
const loading = ref(false);

// 表格列定义
const associatedAssetsColumns = [
  {
    title: '资产类型',
    dataIndex: 'assetType',
    key: 'assetType',
    width: 120,
    align: 'center',
  },
  {
    title: '资产名称（资产编号）',
    dataIndex: 'assetsCode',
    key: 'assetsCode',
    width: 220,
    align: 'center',
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    key: 'targetName',
    width: 150,
    align: 'center',
  },
  {
    title: '挂牌价格（万元）',
    dataIndex: 'listingPrice',
    key: 'listingPrice',
    width: 150,
    align: 'center',
  },
  {
    title: '评估价格（万元）',
    dataIndex: 'evaluatePrice',
    key: 'evaluatePrice',
    width: 150,
    align: 'center',
  },
  {
    title: '转让面积（㎡）',
    dataIndex: 'sellArea',
    key: 'sellArea',
    width: 150,
    align: 'center',
  },
  {
    title: '账面净值（万元）',
    dataIndex: 'bookNetAmount',
    key: 'bookNetAmount',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
    align: 'center',
    fixed: 'right',
  },
];

const transactionsColumns = [
  {
    title: '标的名称',
    dataIndex: 'targetName',
    key: 'targetName',
    width: 180,
    align: 'center',
  },
  {
    title: '是否成交',
    dataIndex: 'dealStatus',
    key: 'dealStatus',
    width: 120,
    align: 'center',
  },
  {
    title: '受让方',
    dataIndex: 'transferee',
    key: 'transferee',
    width: 180,
    align: 'center',
  },
  {
    title: '成交价格（万元）',
    dataIndex: 'dealPrice',
    key: 'dealPrice',
    width: 150,
    align: 'center',
  },
  {
    title: '成交面积',
    dataIndex: 'dealArea',
    key: 'dealArea',
    width: 150,
    align: 'center',
  },
  {
    title: '成交日期',
    dataIndex: 'dealDate',
    key: 'dealDate',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 80,
    align: 'center',
    fixed: 'right',
  },
];

// 表单数据
const formData = reactive({
  // 资产包信息
  code: '',
  name: '',
  type: 0,
  assetsType: 0,
  assetsLocation: [],
  source: 0,
  manageUnit: '',
  reportOrNot: 1,
  operator: '',
  entryClerk: '当前用户',
  createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
  status: 0,
  remark: '',

  // 挂牌信息
  listingOrganization: '',
  listingLocation: [],

  // 转让方信息
  sellName: '',
  sellType: 0,
  jointSell: 0,
  sellLocation: [],
  registration: '',
  registeredCapital: null as number | null,
  enterpriseType: 0,
  economicType: 0,
  legalRepresentative: '',
  scale: 0,
  orgCode: '',
  decisionMaking: 0,
  sellFiles: '',

  // 资产评估核准或备案情况
  evaluateOrg: '',
  approvalOrg: '',
  approvalDate: null,
  evaluateDate: '',
  evaluateReport: '',
  evaluateDateOrg: '',
  lawFirm: '',
  targetPrice: '',
  sellTargetPrice: '',
  bookAmount: '',
  bookNetAmount: '',

  // 披露信息
  listingStartDate: null,
  listingEndDate: null,
  contentDescription: '',
  disclosureFiles: '',

  // 关联资产列表
  associatedAssets: [] as Array<{
    assetType: number;
    assetsCode: string[];
    targetName: string;
    listingPrice: number | null;
    evaluatePrice: number | null;
    sellArea: number | null;
    bookNetAmount: number | null;
    uid?: string;
  }>,

  // 成交信息列表
  transactions: [] as Array<{
    targetName: string;
    dealStatus: number;
    transferee: string;
    dealPrice: number | null;
    dealArea: string;
    dealDate: null,
    uid?: string;
  }>,
});

// 表单验证规则
const rules = {
  // 资产包信息
  name: [{ required: true, message: '请输入转让资产包名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择转让方式', trigger: 'change' }],
  assetsType: [{ required: true, message: '请选择主要资产类型', trigger: 'change' }],
  assetsLocation: [{ required: true, message: '请选择资产所在地区', trigger: 'change' }],
  source: [{ required: true, message: '请选择资产来源', trigger: 'change' }],
  manageUnit: [{ required: true, message: '请选择管理单位', trigger: 'change' }],
  reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
  operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],

  // 转让方信息
  sellName: [{ required: true, message: '请选择转让方名称', trigger: 'change' }],
  sellType: [{ required: true, message: '请选择转让方类型', trigger: 'change' }],
  jointSell: [{ required: true, message: '请选择是否联合转让', trigger: 'change' }],
  sellLocation: [{ required: true, message: '请选择转让方所在地区', trigger: 'change' }],
  registration: [{ required: true, message: '请输入注册地地址', trigger: 'blur' }],
  registeredCapital: [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
  enterpriseType: [{ required: true, message: '请选择企业类型', trigger: 'change' }],
  economicType: [{ required: true, message: '请选择经济类型', trigger: 'change' }],
  legalRepresentative: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
  scale: [{ required: true, message: '请选择经营规模', trigger: 'change' }],
  orgCode: [{ required: true, message: '请输入统一社会信用代码或组织机构代码', trigger: 'blur' }],
  decisionMaking: [{ required: true, message: '请选择内部决策情况', trigger: 'change' }],

  // 披露信息
  contentDescription: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
};

// 选项数据
const enterpriseOptions = [
  { label: '厦门市城市建设发展投资有限公司', value: 0 },
  { label: '厦门市地热资源管理有限公司', value: 1 },
  { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
  { label: '厦门地丰置业有限公司', value: 3 },
  { label: '图智策划咨询（厦门）有限公司', value: 4 },
  { label: '厦门市集众祥和物业管理有限公司', value: 5 },
  { label: '厦门市人居乐业物业服务有限公司', value: 6 },
];

const statusOptions = [
  { label: '草稿', value: 0 },
  { label: '备案', value: 1 },
  { label: '撤回', value: 2, disabled: true },
  { label: '作废', value: 4, disabled: true },
];

// 转让方公司选项
const sellCompanyOptions = [
  { value: '厦门市城市建设发展投资有限公司', label: '厦门市城市建设发展投资有限公司' },
  { value: '厦门市地热资源管理有限公司', label: '厦门市地热资源管理有限公司' },
  { value: '厦门兴地房屋征迁服务有限公司', label: '厦门兴地房屋征迁服务有限公司' },
  { value: '厦门地丰置业有限公司', label: '厦门地丰置业有限公司' },
  { value: '图智策划咨询（厦门）有限公司', label: '图智策划咨询（厦门）有限公司' },
  { value: '厦门市集众祥和物业管理有限公司', label: '厦门市集众祥和物业管理有限公司' },
  { value: '厦门市人居乐业物业服务有限公司', label: '厦门市人居乐业物业服务有限公司' },
];

// 转让方公司详细信息字典
const sellCompanyDict = {
  '厦门市城市建设发展投资有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350203'],
    registration: '厦门市思明区城市路100号',
    registeredCapital: 50000,
    enterpriseType: 1,
    economicType: 0,
    legalRepresentative: '张三',
    scale: 0,
    orgCode: '91350200MA2Y0XXXXX'
  },
  '厦门市地热资源管理有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350206'],
    registration: '厦门市湖里区地热路88号',
    registeredCapital: 20000,
    enterpriseType: 1,
    economicType: 1,
    legalRepresentative: '李四',
    scale: 1,
    orgCode: '91350200MA2Y0YYYYY'
  },
  '厦门兴地房屋征迁服务有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350211'],
    registration: '厦门市集美区兴地路66号',
    registeredCapital: 10000,
    enterpriseType: 1,
    economicType: 2,
    legalRepresentative: '王五',
    scale: 2,
    orgCode: '91350200MA2Y0ZZZZZ'
  },
  '厦门地丰置业有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350205'],
    registration: '厦门市海沧区地丰路77号',
    registeredCapital: 30000,
    enterpriseType: 1,
    economicType: 0,
    legalRepresentative: '赵六',
    scale: 1,
    orgCode: '91350200MA2Y0AAAAA'
  },
  '图智策划咨询（厦门）有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350212'],
    registration: '厦门市同安区图智路99号',
    registeredCapital: 5000,
    enterpriseType: 1,
    economicType: 4,
    legalRepresentative: '孙七',
    scale: 2,
    orgCode: '91350200MA2Y0BBBBB'
  },
  '厦门市集众祥和物业管理有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350213'],
    registration: '厦门市翔安区祥和路55号',
    registeredCapital: 8000,
    enterpriseType: 1,
    economicType: 2,
    legalRepresentative: '周八',
    scale: 2,
    orgCode: '91350200MA2Y0CCCCC'
  },
  '厦门市人居乐业物业服务有限公司': {
    sellType: 0,
    sellLocation: ['350000', '350200', '350203'],
    registration: '厦门市思明区人居路33号',
    registeredCapital: 12000,
    enterpriseType: 1,
    economicType: 1,
    legalRepresentative: '吴九',
    scale: 1,
    orgCode: '91350200MA2Y0DDDDD'
  }
};

// 资产选项数据（模拟数据）
const assetOptions = [
  { label: '总部办公大楼 (ZC001)', value: 'ZC001' },
  { label: '城东物流仓库 (ZC002)', value: 'ZC002' },
  { label: '海滨大道广告牌 (ZC003)', value: 'ZC003' },
  { label: '开发区研发中心 (ZC004)', value: 'ZC004' },
  { label: '中山路临街商铺 (ZC005)', value: 'ZC005' },
  { label: '城南停车场地块 (ZC006)', value: 'ZC006' },
  { label: '西郊生产厂房 (ZC007)', value: 'ZC007' },
  { label: 'CBD金融中心写字楼 (ZC008)', value: 'ZC008' },
  { label: '度假村别墅群 (ZC009)', value: 'ZC009' },
  { label: '历史文化街区建筑 (ZC010)', value: 'ZC010' },
];

// 生成唯一ID
function generateUid() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 资产选项过滤
function filterAssetOption(input: string, option: any) {
  return option.label.toLowerCase().includes(input.toLowerCase());
}

// 转让方式变化处理
function handleTransferTypeChange(value: number) {
  if (value !== 1) {
    formData.listingOrganization = '';
    formData.listingLocation = [];
  }
}

// 转让方名称变化处理
function handleSellNameChange(value: string) {
  const companyInfo = sellCompanyDict[value];
  if (companyInfo) {
    Object.assign(formData, companyInfo);
  }
}

// 添加关联资产
function addAssociatedAsset() {
  formData.associatedAssets.push({
    assetType: 0,
    assetsCode: [],
    targetName: '',
    listingPrice: null,
    evaluatePrice: null,
    sellArea: null,
    bookNetAmount: null,
    uid: generateUid(),
  });
}

// 删除关联资产
function removeAssociatedAsset(index: number) {
  formData.associatedAssets.splice(index, 1);
}

// 资产类型变化处理
function handleAssetTypeChange(record: any, index: number) {
  // 清空资产编号选择
  record.assetsCode = [];
  record.targetName = '';
}

// 资产编号变化处理
function handleAssetsCodeChange(record: any, value: string[], index: number) {
  // 根据选择的资产编号自动生成标的名称
  if (value.length > 0) {
    const selectedAssets = assetOptions.filter(option => value.includes(option.value));
    record.targetName = selectedAssets.map(asset => asset.label.split(' (')[0]).join('、');
  } else {
    record.targetName = '';
  }
}

// 添加成交信息
function addTransaction() {
  formData.transactions.push({
    targetName: '',
    dealStatus: 0,
    transferee: '',
    dealPrice: null,
    dealArea: '',
    dealDate: null,
    uid: generateUid(),
  });
}

// 删除成交信息
function removeTransaction(index: number) {
  formData.transactions.splice(index, 1);
}

// 成交状态变化处理
function handleDealStatusChange(record: any, index: number) {
  if (record.dealStatus === 0) {
    // 如果选择"否"，清空相关字段
    record.transferee = '';
    record.dealPrice = null;
    record.dealArea = '';
    record.dealDate = null;
  }
}

// 提交表单
async function submitForm() {
  try {
    await formRef.value.validate();
    loading.value = true;

    const submitData = { ...formData };
    await saveOrUpdate(submitData, isEditMode.value);

    message.success(isEditMode.value ? '更新成功' : '新增成功');
    router.push('/transfer/info');
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
}

// 重置表单
function resetForm() {
  formRef.value.resetFields();
}

// 初始化
onMounted(async () => {
  const { id } = route.params;
  if (id) {
    isEditMode.value = true;
    submitButtonText.value = '更新';

    try {
      const data = await getDetail(id as string);
      if (data) {
        Object.assign(formData, data);
      }
    } catch (error) {
      message.error('加载详情失败');
    }
  }
});
</script>

<style lang="less" scoped>
.transfer-form {
  .simple-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .form-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;

      .form-card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #333;

        .title-icon {
          margin-right: 8px;
          color: #1890ff;
        }
      }
    }

    .form-card-body {
      padding: 24px;
    }
  }

  .form-footer {
    text-align: center;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .help-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 4px;
  }

  .empty-hint {
    text-align: center;
    color: #999;
    font-size: 14px;
    padding: 40px 20px;
    background: #fafafa;
    border-radius: 4px;
  }

  // 表格内表单项样式
  :deep(.ant-table-tbody .ant-form-item) {
    margin-bottom: 0;
  }

  :deep(.ant-table-tbody .ant-form-item-explain-error) {
    position: absolute;
    z-index: 1000;
    background: white;
    border: 1px solid #ff4d4f;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  // 表格中单选按钮组样式
  :deep(.ant-table-tbody .ant-radio-group) {
    display: flex;
    flex-wrap: nowrap;
    white-space: nowrap;
  }

  :deep(.ant-table-tbody .ant-radio-wrapper) {
    margin-right: 8px;
    white-space: nowrap;
  }

  // 表格列宽度优化
  :deep(.ant-table-thead th) {
    white-space: nowrap;
  }

  :deep(.ant-table-tbody td) {
    padding: 8px 4px;
  }

  // 响应式样式
  @media (max-width: 768px) {
    .form-card-body {
      padding: 16px;
    }

    :deep(.ant-form-item-label) {
      width: 120px;
      min-width: 120px;
    }

    :deep(.ant-col) {
      &.ant-col-8 {
        flex: 0 0 100%;
        max-width: 100%;
      }
    }
  }
}
</style>