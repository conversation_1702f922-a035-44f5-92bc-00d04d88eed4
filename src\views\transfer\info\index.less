// 转让信息列表页面样式
.transfer-list {
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }

    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }

    // 状态标签样式
    .ant-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
      
      &.ant-tag-blue {
        background-color: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;
      }
      
      &.ant-tag-success {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
      
      &.ant-tag-warning {
        background-color: #fff7e6;
        color: #faad14;
        border: 1px solid #ffd591;
      }
      
      &.ant-tag-error {
        background-color: #fff1f0;
        color: #f5222d;
        border: 1px solid #ffa39e;
      }
    }

    // 合计行样式
    .ant-table-summary {
      background-color: #f2f6fc;
      font-weight: bold;
      
      .ant-table-summary-cell {
        background-color: #f2f6fc;
        color: #409eff;
      }
    }

    // 固定列阴影
    .ant-table-fixed-right {
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.12);
    }
  }

  // 搜索表单样式
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      font-weight: 500;
    }

    // 展开/收起按钮样式
    .ant-form-item-control-input-content {
      .ant-btn-link {
        padding: 0;
        height: auto;
        line-height: 1.5;
        color: #1890ff;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  // 操作按钮区域样式
  .ant-table-title {
    padding: 16px 0;
    
    .ant-btn {
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 表格工具栏样式
  .ant-table-header {
    .ant-table-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  // 分页样式
  .ant-pagination {
    margin-top: 16px;
    text-align: right;
    
    .ant-pagination-total-text {
      margin-right: 16px;
    }
  }

  // 空状态样式
  .ant-empty {
    padding: 40px 0;
    
    .ant-empty-description {
      color: #909399;
    }
  }

  // 加载状态样式
  .ant-spin-container {
    min-height: 200px;
  }

  // 表格设置下拉菜单样式
  .ant-dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
    
    .ant-dropdown-menu-item {
      padding: 8px 16px;
      
      .ant-checkbox-wrapper {
        width: 100%;
      }
    }
  }

  // 响应式样式
  @media (max-width: 768px) {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
    
    .ant-form {
      .ant-form-item {
        margin-bottom: 12px;
      }
    }
    
    .ant-table-title {
      .ant-btn {
        margin-bottom: 8px;
        margin-right: 4px;
      }
    }
  }
}

// 导入模态框样式
.transfer-import-modal {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    text-align: center;
    padding: 40px 20px;
    
    &:hover {
      border-color: #1890ff;
    }
    
    .ant-upload-drag-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    .ant-upload-text {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 8px;
    }
    
    .ant-upload-hint {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
  
  .upload-tip {
    margin-top: 16px;
    padding: 12px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    
    .anticon {
      color: #1890ff;
      margin-right: 8px;
    }
  }
  
  .template-download {
    text-align: center;
    margin-bottom: 24px;
    
    .ant-btn {
      font-size: 14px;
      height: 36px;
      padding: 0 20px;
    }
  }
}

// 确保表格内容不换行
.ant-table .ant-table-tbody > tr > td {
  white-space: nowrap;
  
  // 当内容超出时显示省略号
  &.ant-table-cell-ellipsis {
    .ant-table-cell-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 数值列右对齐
.ant-table .ant-table-tbody > tr > td.ant-table-cell-align-right {
  text-align: right;
  font-family: 'Courier New', monospace;
}
