<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
        <a-button type="primary" preIcon="ant-design:import-outlined" @click="handleImport">导入</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport"
          :disabled="selectedRowKeys.length === 0">导出</a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
      <template #dealStatus="{ record }">
        <a-tag :color="record.dealStatus === 1 ? 'success' : 'default'">
          {{ record.dealStatus === 1 ? '是' : '否' }}
        </a-tag>
      </template>
      <template #assetsType="{ record }">
        {{ getAssetsTypeText(record.assetsType) }}
      </template>
      <template #type="{ record }">
        {{ getTransferTypeText(record.type) }}
      </template>
      <template #listingPrice="{ record }">
        {{ record.listingPrice ? record.listingPrice.toFixed(2) : '' }}
      </template>
      <template #dealPrice="{ record }">
        {{ record.dealPrice ? record.dealPrice.toFixed(2) : '' }}
      </template>
    </BasicTable>

    <!-- 导入模态框 -->
    <ImportModal @register="registerImportModal" @success="handleImportSuccess" />
  </div>
</template>

<script lang="ts" name="transfer-list" setup>
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema } from './transfer.data';
  import { list, deleteTransfer, exportTransfer, exportAllTransfer } from './transfer.api';
  import { mapTableTotalSummary } from '/@/utils/common/compUtils';
  import ImportModal from './ImportModal.vue';

  const { createMessage } = useMessage();
  const router = useRouter();

  // 导入模态框
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'transfer-list',
    tableProps: {
      title: '转让信息列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'transfer_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
      // 显示合计行
      showSummary: true,
      summaryFunc: (data) => {
        // 使用工具函数自动计算合计
        const totals = mapTableTotalSummary(data, [
          'listingPrice',
          'dealPrice',
        ]);

        // 格式化数值
        Object.keys(totals).forEach((key) => {
          if (typeof totals[key] === 'number') {
            totals[key] = Number(totals[key].toFixed(2));
          }
        });

        return [totals];
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/transfer/info/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/transfer/info/edit/${record.id}`);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteTransfer({ id: record.id }, reload);
  }

  /**
   * 导出选中数据
   */
  async function handleExport() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    await exportTransfer({ ids: selectedRowKeys.value });
  }

  /**
   * 导出全部数据
   */
  async function handleExportAll() {
    await exportAllTransfer({});
  }

  /**
   * 导入事件
   */
  function handleImport() {
    openImportModal(true);
  }

  /**
   * 导入成功回调
   */
  function handleImportSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  /**
   * 获取状态文本
   */
  function getStatusText(status: number) {
    const statusMap = {
      0: '草稿',
      1: '备案',
      2: '撤回',
      4: '作废'
    };
    return statusMap[status] || '';
  }

  /**
   * 获取状态颜色
   */
  function getStatusColor(status: number) {
    const colorMap = {
      0: 'blue',
      1: 'success',
      2: 'warning',
      4: 'error'
    };
    return colorMap[status] || 'default';
  }

  /**
   * 获取资产类型文本
   */
  function getAssetsTypeText(type: number) {
    const typeMap = {
      0: '土地',
      1: '房屋',
      2: '设备',
      3: '广告位',
      4: '其他'
    };
    return typeMap[type] || '';
  }

  /**
   * 获取转让方式文本
   */
  function getTransferTypeText(type: number) {
    const typeMap = {
      0: '厦门公开转让(进场)',
      1: '异地公开转让(进场)',
      2: '公开转让(非进场)',
      3: '其他转让',
      4: '其他方式转让(协议)',
      5: '无偿划转'
    };
    return typeMap[type] || '';
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
