import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();

enum Api {
  list = '/mock/transferInfo/list',
  save = '/mock/transferInfo/save',
  update = '/mock/transferInfo/update',
  delete = '/mock/transferInfo/delete',
  detail = '/mock/transferInfo/detail',
  export = '/mock/transferInfo/export',
  exportAll = '/mock/transferInfo/exportAll',
  import = '/mock/transferInfo/import',
  downloadTemplate = '/mock/transferInfo/downloadTemplate',
}

/**
 * 获取转让信息列表
 */
export const list = (params?: any) => {
  return defHttp.get<any>({
    url: Api.list,
    params,
  });
};

/**
 * 获取转让信息详情
 */
export const getDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.detail,
    params: { id },
  });
};

/**
 * 保存或更新转让信息
 */
export const saveOrUpdate = (params: any, isUpdate = false) => {
  return defHttp.post<any>(
    {
      url: isUpdate ? Api.update : Api.save,
      params,
    },
    {
      successMessageMode: 'message',
    }
  );
};

/**
 * 删除转让信息
 */
export const deleteTransfer = (params: { id: string }, handleSuccess: () => void) => {
  return defHttp.delete<any>(
    {
      url: Api.delete,
      params,
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        handleSuccess();
      },
    }
  );
};

/**
 * 导出选中的转让信息
 */
export const exportTransfer = (params: { ids: string[] }) => {
  return defHttp.post<any>(
    {
      url: Api.export,
      params,
      responseType: 'blob',
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        createMessage.success('导出成功');
      },
    }
  );
};

/**
 * 导出全部转让信息
 */
export const exportAllTransfer = (params: any) => {
  return defHttp.post<any>(
    {
      url: Api.exportAll,
      params,
      responseType: 'blob',
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        createMessage.success('导出成功');
      },
    }
  );
};

/**
 * 导入转让信息
 */
export const importTransfer = (params: any) => {
  return defHttp.post<any>(
    {
      url: Api.import,
      params,
    },
    {
      successMessageMode: 'message',
    }
  );
};

/**
 * 下载导入模板
 */
export const downloadTemplate = () => {
  return defHttp.get<any>(
    {
      url: Api.downloadTemplate,
      responseType: 'blob',
    },
    {
      successMessageMode: 'message',
      onSuccess: () => {
        createMessage.success('模板下载成功');
      },
    }
  );
};

/**
 * 获取资产选项（用于关联资产选择）
 */
export const getAssetOptions = (params?: { keyword?: string; assetType?: number }) => {
  return defHttp.get<any>({
    url: '/mock/assets/options',
    params,
  });
};

/**
 * 获取转让方选项
 */
export const getTransferorOptions = (params?: { keyword?: string }) => {
  return defHttp.get<any>({
    url: '/mock/transferor/options',
    params,
  });
};
