import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '资产包编号',
    dataIndex: 'code',
    width: 160,
    fixed: 'left',
  },
  {
    title: '转让资产包名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '转让方式',
    dataIndex: 'type',
    width: 150,
    slots: { customRender: 'type' },
  },
  {
    title: '主要资产类型',
    dataIndex: 'assetsType',
    width: 120,
    slots: { customRender: 'assetsType' },
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 200,
    ellipsis: true,
    customRender: ({ record }) => {
      const unitMap = {
        0: '厦门市城市建设发展投资有限公司',
        1: '厦门市地热资源管理有限公司',
        2: '厦门兴地房屋征迁服务有限公司',
        3: '厦门地丰置业有限公司',
        4: '图智策划咨询（厦门）有限公司',
        5: '厦门市集众祥和物业管理有限公司',
        6: '厦门市人居乐业物业服务有限公司'
      };
      return unitMap[record.manageUnit] || '';
    },
  },
  {
    title: '挂牌价格(万元)',
    dataIndex: 'listingPrice',
    width: 130,
    align: 'right',
    slots: { customRender: 'listingPrice' },
  },
  {
    title: '转让方名称',
    dataIndex: 'sellName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    slots: { customRender: 'status' },
  },
  {
    title: '是否成交',
    dataIndex: 'dealStatus',
    width: 90,
    slots: { customRender: 'dealStatus' },
  },
  {
    title: '受让方',
    dataIndex: 'transferee',
    width: 180,
    ellipsis: true,
  },
  {
    title: '成交价格(万元)',
    dataIndex: 'dealPrice',
    width: 130,
    align: 'right',
    slots: { customRender: 'dealPrice' },
  },
  {
    title: '挂牌开始时间',
    dataIndex: 'listingStartDate',
    width: 120,
  },
  {
    title: '挂牌截止时间',
    dataIndex: 'listingEndDate',
    width: 120,
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '资产名称',
    dataIndex: 'assetName',
    width: 150,
    ellipsis: true,
    defaultHidden: true,
  },
  {
    title: '资产编号',
    dataIndex: 'assetCode',
    width: 120,
    defaultHidden: true,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 100,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 100,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '内容描述',
    dataIndex: 'contentDescription',
    width: 200,
    ellipsis: true,
    defaultHidden: true,
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资产包名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包名称',
    },
  },
  {
    field: 'code',
    label: '资产包编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包编号',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2 },
        { label: '作废', value: 4 },
      ],
    },
  },
  {
    field: 'dealStatus',
    label: '是否成交',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否成交',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'Select',
    componentProps: {
      placeholder: '请选择管理单位',
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
  },
  {
    field: 'sellName',
    label: '转让方名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入转让方名称',
    },
  },
  {
    field: 'assetName',
    label: '资产名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'assetCode',
    label: '资产编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'assetsType',
    label: '主要资产类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择资产类型',
      options: [
        { label: '土地', value: 0 },
        { label: '房屋', value: 1 },
        { label: '设备', value: 2 },
        { label: '广告位', value: 3 },
        { label: '其他', value: 4 },
      ],
    },
  },
  {
    field: 'type',
    label: '转让方式',
    component: 'Select',
    componentProps: {
      placeholder: '请选择转让方式',
      options: [
        { label: '厦门公开转让(进场)', value: 0 },
        { label: '异地公开转让(进场)', value: 1 },
        { label: '公开转让(非进场)', value: 2 },
        { label: '其他转让', value: 3 },
        { label: '其他方式转让(协议)', value: 4 },
        { label: '无偿划转', value: 5 },
      ],
    },
  },
  {
    field: 'priceRange',
    label: '挂牌价格范围',
    component: 'JRangeNumber',
    componentProps: {
      placeholder: ['最低价', '最高价'],
    },
  },
  {
    field: 'listingDateRange',
    label: '挂牌时间范围',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    field: 'transferee',
    label: '受让方',
    component: 'Input',
    componentProps: {
      placeholder: '请输入受让方',
    },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTimeRange',
    label: '录入时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
  },
  {
    field: 'targetName',
    label: '标的名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的名称',
    },
  },
];
