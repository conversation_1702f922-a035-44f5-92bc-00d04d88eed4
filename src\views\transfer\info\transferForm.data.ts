import { FormSchema } from '/@/components/Table';
import { BasicColumn } from '/@/components/Table';

// 基本信息表单配置
export const basicInfoSchema: FormSchema[] = [
  {
    field: 'code',
    label: '转让资产包编号',
    component: 'Input',
    componentProps: {
      placeholder: '保存后系统自动生成',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'name',
    label: '转让资产包名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入转让资产包名称',
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请输入转让资产包名称',
      },
    ],
  },
  {
    field: 'type',
    label: '转让方式',
    component: 'Select',
    componentProps: {
      placeholder: '请选择转让方式',
      options: [
        { label: '厦门公开转让(进场)', value: 0 },
        { label: '异地公开转让(进场)', value: 1 },
        { label: '公开转让(非进场)', value: 2 },
        { label: '其他转让', value: 3 },
        { label: '其他方式转让(协议)', value: 4 },
        { label: '无偿划转', value: 5 },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择转让方式',
      },
    ],
  },
  {
    field: 'assetsType',
    label: '主要资产类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择主要资产类型',
      options: [
        { label: '土地', value: 0 },
        { label: '房屋', value: 1 },
        { label: '设备', value: 2 },
        { label: '广告位', value: 3 },
        { label: '其他', value: 4 },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择主要资产类型',
      },
    ],
  },
  {
    field: 'assetsLocation',
    label: '资产所在地区',
    component: 'Cascader',
    componentProps: {
      placeholder: '请选择资产所在地区',
      options: [
        {
          value: 'fujian',
          label: '福建省',
          children: [
            {
              value: 'xiamen',
              label: '厦门市',
              children: [
                { value: 'siming', label: '思明区' },
                { value: 'huli', label: '湖里区' },
                { value: 'jimei', label: '集美区' },
                { value: 'haicang', label: '海沧区' },
                { value: 'tongan', label: '同安区' },
                { value: 'xiang_an', label: '翔安区' },
              ],
            },
          ],
        },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择资产所在地区',
      },
    ],
  },
  {
    field: 'source',
    label: '资产来源',
    component: 'Select',
    componentProps: {
      placeholder: '请选择资产来源',
      options: [
        { label: '企业实物资产', value: 0 },
        { label: '行政事业单位实物资产', value: 1 },
        { label: '其他', value: 2 },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择资产来源',
      },
    ],
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'Select',
    componentProps: {
      placeholder: '请选择管理单位',
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: 0 },
        { label: '厦门市地热资源管理有限公司', value: 1 },
        { label: '厦门兴地房屋征迁服务有限公司', value: 2 },
        { label: '厦门地丰置业有限公司', value: 3 },
        { label: '图智策划咨询（厦门）有限公司', value: 4 },
        { label: '厦门市集众祥和物业管理有限公司', value: 5 },
        { label: '厦门市人居乐业物业服务有限公司', value: 6 },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择管理单位',
      },
    ],
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'Select',
    componentProps: {
      placeholder: '请选择是否报送国资委',
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择是否报送国资委',
      },
    ],
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请输入经办人',
      },
    ],
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    componentProps: {
      placeholder: '录入人',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'Input',
    componentProps: {
      placeholder: '录入时间',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '草稿', value: 0 },
        { label: '备案', value: 1 },
        { label: '撤回', value: 2, disabled: true },
        { label: '作废', value: 4, disabled: true },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择状态',
      },
    ],
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 3,
    },
  },
];

// 挂牌信息表单配置
export const listingInfoSchema: FormSchema[] = [
  {
    field: 'listingOrganization',
    label: '挂牌机构',
    component: 'Input',
    componentProps: {
      placeholder: '请输入挂牌机构',
    },
    ifShow: ({ values }) => values.type === 1,
    rules: [
      {
        required: true,
        message: '请输入挂牌机构',
        trigger: 'blur',
      },
    ],
  },
  {
    field: 'listingLocation',
    label: '挂牌机构-所在地区',
    component: 'Cascader',
    componentProps: {
      placeholder: '请选择挂牌机构所在地区',
      options: [
        {
          value: 'fujian',
          label: '福建省',
          children: [
            {
              value: 'xiamen',
              label: '厦门市',
              children: [
                { value: 'siming', label: '思明区' },
                { value: 'huli', label: '湖里区' },
                { value: 'jimei', label: '集美区' },
                { value: 'haicang', label: '海沧区' },
                { value: 'tongan', label: '同安区' },
                { value: 'xiang_an', label: '翔安区' },
              ],
            },
          ],
        },
      ],
    },
    ifShow: ({ values }) => values.type === 1,
    rules: [
      {
        required: true,
        message: '请选择挂牌机构所在地区',
        trigger: 'change',
      },
    ],
  },
];

// 转让方信息表单配置
export const transferorInfoSchema: FormSchema[] = [
  {
    field: 'sellName',
    label: '转让方名称',
    component: 'Select',
    componentProps: {
      placeholder: '请选择转让方名称',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      options: [
        { label: '厦门市城市建设发展投资有限公司', value: '厦门市城市建设发展投资有限公司' },
        { label: '厦门市地热资源管理有限公司', value: '厦门市地热资源管理有限公司' },
        { label: '厦门兴地房屋征迁服务有限公司', value: '厦门兴地房屋征迁服务有限公司' },
        { label: '厦门地丰置业有限公司', value: '厦门地丰置业有限公司' },
        { label: '图智策划咨询（厦门）有限公司', value: '图智策划咨询（厦门）有限公司' },
      ],
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择转让方名称',
      },
    ],
  },
  {
    field: 'sellType',
    label: '转让方类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择转让方类型',
      options: [
        { label: '法人单位', value: 0 },
        { label: '非法人单位', value: 1 },
      ],
    },
  },
  {
    field: 'jointSell',
    label: '是否联合转让',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
  },
  {
    field: 'sellLocation',
    label: '转让方所在地区',
    component: 'Cascader',
    componentProps: {
      placeholder: '请选择转让方所在地区',
      options: [
        {
          value: 'fujian',
          label: '福建省',
          children: [
            {
              value: 'xiamen',
              label: '厦门市',
              children: [
                { value: 'siming', label: '思明区' },
                { value: 'huli', label: '湖里区' },
                { value: 'jimei', label: '集美区' },
                { value: 'haicang', label: '海沧区' },
                { value: 'tongan', label: '同安区' },
                { value: 'xiang_an', label: '翔安区' },
              ],
            },
          ],
        },
      ],
    },
  },
  {
    field: 'registration',
    label: '注册地地址',
    component: 'Input',
    componentProps: {
      placeholder: '请输入注册地地址',
    },
  },
  {
    field: 'registeredCapital',
    label: '注册资本（万元）',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入注册资本',
      precision: 2,
      min: 0,
      controls: false,
      style: { width: '100%' },
    },
  },
  {
    field: 'enterpriseType',
    label: '企业类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择企业类型',
      options: [
        { label: '全民所有制企业', value: 0 },
        { label: '有限责任公司', value: 1 },
        { label: '股份有限公司', value: 2 },
        { label: '集体所有制', value: 3 },
        { label: '合伙企业', value: 4 },
        { label: '其他', value: 5 },
      ],
    },
  },
  {
    field: 'economicType',
    label: '经济类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择经济类型',
      options: [
        { label: '国有独资企业', value: 0 },
        { label: '国有全资企业', value: 1 },
        { label: '国有控股企业', value: 2 },
        { label: '国有实际控制企业', value: 3 },
        { label: '国有参股企业', value: 4 },
        { label: '事业单位', value: 5 },
        { label: '其他', value: 6 },
      ],
    },
  },
  {
    field: 'legalRepresentative',
    label: '法定代表人',
    component: 'Input',
    componentProps: {
      placeholder: '请输入法定代表人',
    },
  },
  {
    field: 'scale',
    label: '经营规模',
    component: 'Select',
    componentProps: {
      placeholder: '请选择经营规模',
      options: [
        { label: '大型', value: 0 },
        { label: '中型', value: 1 },
        { label: '小型', value: 2 },
        { label: '微型', value: 3 },
      ],
    },
  },
  {
    field: 'orgCode',
    label: '统一社会信用代码或组织机构代码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入统一社会信用代码或组织机构代码',
    },
  },
  {
    field: 'decisionMaking',
    label: '内部决策情况',
    component: 'Select',
    componentProps: {
      placeholder: '请选择内部决策情况',
      options: [
        { label: '董事会决议', value: 0 },
        { label: '股东会决议', value: 1 },
        { label: '总经理办公会决议', value: 2 },
        { label: '其他类型', value: 3 },
      ],
    },
  },
];

// 资产评估核准或备案情况表单配置
export const evaluationInfoSchema: FormSchema[] = [
  {
    field: 'evaluateOrg',
    label: '评估机构',
    component: 'Input',
    componentProps: {
      placeholder: '请输入评估机构',
    },
  },
  {
    field: 'approvalOrg',
    label: '评估核准（备案）机构',
    component: 'Input',
    componentProps: {
      placeholder: '请输入评估核准（备案）机构',
    },
  },
  {
    field: 'approvalDate',
    label: '核准（备案）日期',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择核准（备案）日期',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
  },
  {
    field: 'evaluateDate',
    label: '评估基准日',
    component: 'Input',
    componentProps: {
      placeholder: '请输入评估基准日',
    },
  },
  {
    field: 'evaluateReport',
    label: '评估报告文号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入评估报告文号',
    },
  },
  {
    field: 'evaluateDateOrg',
    label: '评估基准日审计机构',
    component: 'Input',
    componentProps: {
      placeholder: '请输入评估基准日审计机构',
    },
  },
  {
    field: 'lawFirm',
    label: '律师事务所',
    component: 'Input',
    componentProps: {
      placeholder: '请输入律师事务所',
    },
  },
  {
    field: 'targetPrice',
    label: '标的评估值（万元）',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标的评估值',
    },
  },
  {
    field: 'sellTargetPrice',
    label: '转让标的评估总值（万元）',
    component: 'Input',
    componentProps: {
      placeholder: '请输入转让标的评估总值',
    },
  },
  {
    field: 'bookAmount',
    label: '账面原值（万元）',
    component: 'Input',
    componentProps: {
      placeholder: '请输入账面原值',
    },
  },
  {
    field: 'bookNetAmount',
    label: '账面净值（万元）',
    component: 'Input',
    componentProps: {
      placeholder: '请输入账面净值',
    },
  },
];

// 披露信息表单配置
export const disclosureInfoSchema: FormSchema[] = [
  {
    field: 'listingStartDate',
    label: '挂牌（公示）开始时间',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择挂牌（公示）开始时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    ifShow: ({ values }) => values.type === 1 || values.type === 2,
    rules: [
      {
        required: true,
        message: '请选择挂牌（公示）开始时间',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'listingEndDate',
    label: '挂牌（公示）截止时间',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择挂牌（公示）截止时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    ifShow: ({ values }) => values.type === 1 || values.type === 2,
    rules: [
      {
        required: true,
        message: '请选择挂牌（公示）截止时间',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'contentDescription',
    label: '内容描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入内容描述',
      rows: 4,
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请输入内容描述',
      },
    ],
  },
];

// 关联资产表格列配置
export const assetColumns: BasicColumn[] = [
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 120,
    customRender: ({ record }) => {
      const typeMap = {
        0: '土地',
        1: '房屋',
        2: '设备',
        3: '广告位',
        4: '其他'
      };
      return typeMap[record.assetType] || '';
    },
  },
  {
    title: '资产名称（资产编号）',
    dataIndex: 'assetsCode',
    width: 220,
    ellipsis: true,
  },
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '挂牌价格（万元）',
    dataIndex: 'listingPrice',
    width: 150,
    align: 'right',
    customRender: ({ record }) => {
      return record.listingPrice ? record.listingPrice.toFixed(2) : '';
    },
  },
  {
    title: '评估价格（万元）',
    dataIndex: 'evaluatePrice',
    width: 150,
    align: 'right',
    customRender: ({ record }) => {
      return record.evaluatePrice ? record.evaluatePrice.toFixed(2) : '';
    },
  },
  {
    title: '转让面积（㎡）',
    dataIndex: 'sellArea',
    width: 150,
    align: 'right',
    customRender: ({ record }) => {
      return record.sellArea ? record.sellArea.toFixed(2) : '';
    },
  },
  {
    title: '账面净值（万元）',
    dataIndex: 'bookNetAmount',
    width: 150,
    align: 'right',
    customRender: ({ record }) => {
      return record.bookNetAmount ? record.bookNetAmount.toFixed(2) : '';
    },
  },
];

// 成交信息表格列配置
export const dealColumns: BasicColumn[] = [
  {
    title: '标的名称',
    dataIndex: 'targetName',
    width: 180,
    ellipsis: true,
  },
  {
    title: '是否成交',
    dataIndex: 'dealStatus',
    width: 120,
    customRender: ({ record }) => {
      return record.dealStatus === 1 ? '是' : '否';
    },
  },
  {
    title: '受让方',
    dataIndex: 'transferee',
    width: 180,
    ellipsis: true,
  },
  {
    title: '成交价格（万元）',
    dataIndex: 'dealPrice',
    width: 150,
    align: 'right',
    customRender: ({ record }) => {
      return record.dealPrice ? record.dealPrice.toFixed(2) : '';
    },
  },
  {
    title: '成交日期',
    dataIndex: 'dealDate',
    width: 120,
  },
  {
    title: '合同开始日期',
    dataIndex: 'dealBeginDate',
    width: 130,
  },
  {
    title: '合同结束日期',
    dataIndex: 'dealEndDate',
    width: 130,
  },
];
