// 数据上报记录列表样式
.upload-record-list {
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }

    .ant-table-cell {
      line-height: 26px;
      font-size: 14px;
    }
  }

  // 状态标签样式
  .ant-tag {
    &.ant-tag-success {
      background-color: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    &.ant-tag-error {
      background-color: #fff1f0;
      color: #f5222d;
      border: 1px solid #ffa39e;
    }
  }

  // 操作按钮样式
  .ant-btn {
    &.ant-btn-text {
      padding: 4px 10px;
    }
  }
}
