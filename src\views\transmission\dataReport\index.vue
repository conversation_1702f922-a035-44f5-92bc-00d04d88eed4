<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:upload-outlined" @click="handleUpload" 
          :disabled="selectedRowKeys.length === 0">立即上传</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="upload-record-list" setup>
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Modal } from 'ant-design-vue';
  import { columns, searchFormSchema } from './uploadRecord.data';
  import { list, reuploadRecord, batchReupload } from './uploadRecord.api';

  const { createMessage } = useMessage();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'upload-record-list',
    tableProps: {
      title: '数据上报记录',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'upload_record_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'uploadTime', order: 'desc' }, params);
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 批量立即上传事件
   */
  async function handleUpload() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }

    // 检查选中的记录是否都是上传失败的
    const tableData = tableContext[1].getDataSource();
    const selectedRecords = tableData.filter(item => selectedRowKeys.value.includes(item.id));
    const allFailed = selectedRecords.every(record => record.uploadResult === 0);
    const allSuccess = selectedRecords.every(record => record.uploadResult === 1);

    if (allSuccess) {
      createMessage.warning('所选择的数据上传结果为上传成功，无需上传');
      return;
    }

    if (!allFailed) {
      createMessage.warning('请仅选择上传失败的数据进行重新上传');
      return;
    }

    Modal.confirm({
      title: '提示',
      content: `确定要重新上传选中的 ${selectedRowKeys.value.length} 个数据包吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await batchReupload({ ids: selectedRowKeys.value });
          createMessage.success('批量重新上传成功！');
          reload();
        } catch (error) {
          createMessage.error('批量重新上传失败');
        }
      },
    });
  }

  /**
   * 单个重新上传事件
   */
  async function handleReupload(record: Recordable) {
    try {
      createMessage.info(`开始重新上传数据包：${record.packageName}`);
      await reuploadRecord({ id: record.id });
      createMessage.success('数据重新上传成功');
      reload();
    } catch (error) {
      createMessage.error('数据重新上传失败');
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    const actions: ActionItem[] = [];
    
    // 只有上传失败的记录才显示重新上传按钮
    if (record.uploadResult === 0) {
      actions.push({
        label: '立即上传',
        onClick: handleReupload.bind(null, record),
      });
    }

    return actions;
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
