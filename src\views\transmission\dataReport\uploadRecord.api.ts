import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/uploadRecord/list',
  reupload = '/mock/uploadRecord/reupload',
  batchReupload = '/mock/uploadRecord/batchReupload',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 重新上传单个记录
 * @param params
 */
export const reuploadRecord = (params) => defHttp.post({ url: Api.reupload, params });

/**
 * 批量重新上传
 * @param params
 */
export const batchReupload = (params) => defHttp.post({ url: Api.batchReupload, params });
