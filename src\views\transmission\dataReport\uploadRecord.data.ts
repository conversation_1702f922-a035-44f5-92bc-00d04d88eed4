import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '数据包名称',
    dataIndex: 'packageName',
    width: 180,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: 120,
  },
  {
    title: '文件数量',
    dataIndex: 'fileCount',
    width: 100,
    customRender: ({ text }) => {
      return `${text}个`;
    },
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    width: 160,
  },
  {
    title: '上传结果',
    dataIndex: 'uploadResult',
    width: 100,
    customRender: ({ text }) => {
      if (text === 1) {
        return render.renderTag('上传成功', 'success');
      } else {
        return render.renderTag('上传失败', 'error');
      }
    },
  },
  {
    title: '接收时间',
    dataIndex: 'receiveTime',
    width: 160,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '推送到前置机时间',
    dataIndex: 'pushTime',
    width: 180,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '国资委抓取时间',
    dataIndex: 'fetchTime',
    width: 160,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '反馈时间',
    dataIndex: 'feedbackTime',
    width: 160,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '反馈状态',
    dataIndex: 'feedbackStatus',
    width: 120,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '反馈内容',
    dataIndex: 'feedbackContent',
    width: 180,
    ellipsis: true,
    customRender: ({ text, record }) => {
      return record.uploadResult === 1 ? text || '--' : '--';
    },
  },
  {
    title: '文件类型',
    dataIndex: 'fileType',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'businessTypes',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择业务类型',
      options: [
        { label: '土地', value: 'land' },
        { label: '房屋', value: 'house' },
        { label: '广告位', value: 'advert' },
        { label: '设备', value: 'equipment' },
        { label: '其他资产', value: 'other' },
        { label: '租赁', value: 'rent' },
        { label: '租赁中止', value: 'rentAbort' },
        { label: '招租公告', value: 'rentAd' },
        { label: '租金明细', value: 'rentDetail' },
        { label: '转让', value: 'transfer' },
        { label: '空置/闲置', value: 'vacant' },
        { label: '占用', value: 'occupy' },
        { label: '借出', value: 'lend' },
        { label: '自用', value: 'self' },
        { label: '制度信息', value: 'systemInfo' },
        { label: '租金一览', value: 'rentOverview' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: 'uploadTimeRange',
    label: '上传时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 6 },
  },
  {
    field: 'uploadResult',
    label: '上传结果',
    component: 'Select',
    componentProps: {
      placeholder: '请选择上传结果',
      allowClear: true,
      options: [
        { label: '上传成功', value: 1 },
        { label: '上传失败', value: 0 },
      ],
    },
    colProps: { span: 6 },
  },
];
