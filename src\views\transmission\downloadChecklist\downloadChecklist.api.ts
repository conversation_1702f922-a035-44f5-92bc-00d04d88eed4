import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/mock/downloadChecklist/list',
  startQuery = '/mock/downloadChecklist/startQuery',
  fileDetails = '/mock/downloadChecklist/fileDetails',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 发起查询
 * @param params
 */
export const startQuery = (params) => defHttp.post({ url: Api.startQuery, params });

/**
 * 获取文件详情
 * @param id
 */
export const getFileDetails = (id) => defHttp.get({ url: Api.fileDetails, params: { id } });
