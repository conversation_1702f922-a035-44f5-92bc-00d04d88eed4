import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

// 业务类型选项
export const businessTypeOptions = [
  { label: '土地', value: 'land' },
  { label: '房屋', value: 'house' },
  { label: '广告位', value: 'advert' },
  { label: '设备', value: 'equipment' },
  { label: '其他', value: 'other' },
  { label: '租赁', value: 'rent' },
  { label: '租赁中止', value: 'rentAbort' },
  { label: '招租公告', value: 'rentAd' },
  { label: '租金明细', value: 'rentDetail' },
  { label: '转让', value: 'transfer' },
  { label: '空置/闲置', value: 'vacant' },
  { label: '占用', value: 'occupy' },
  { label: '借出', value: 'lend' },
  { label: '自用', value: 'self' },
  { label: '制度信息', value: 'systemInfo' },
  { label: '租金一览', value: 'rentOverview' },
  { label: '资产二维码', value: 'assetQRCode' },
];

// 主表格列配置
export const columns: BasicColumn[] = [
  {
    title: '查询时间',
    dataIndex: 'queryTime',
    width: 180,
    fixed: 'left',
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: 120,
    customRender: ({ text }) => {
      if (!text || text === '--') return '--';
      const option = businessTypeOptions.find(item => item.value === text);
      return option ? option.label : text;
    },
  },
  {
    title: '查询结果',
    dataIndex: 'queryResult',
    width: 150,
    slots: { customRender: 'queryResult' },
  },
  {
    title: '结果说明',
    dataIndex: 'resultDescription',
    width: 220,
    slots: { customRender: 'resultDescription' },
  },
  {
    title: '文件数量',
    dataIndex: 'fileCount',
    width: 100,
    slots: { customRender: 'fileCount' },
  },
  {
    title: '查询方式',
    dataIndex: 'queryMethod',
    width: 120,
    customRender: ({ text }) => {
      const methodMap = {
        'auto': '系统自动查询',
        'manual': '人工手动查询'
      };
      return methodMap[text] || text;
    },
  },
  {
    title: '文件清单',
    dataIndex: 'fileList',
    width: 120,
    slots: { customRender: 'fileList' },
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'businessTypes',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      options: businessTypeOptions,
      placeholder: '请选择业务类型',
      maxTagCount: 2,
    },
    colProps: { span: 6 },
  },
  {
    field: 'queryTimeRange',
    label: '查询时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 6 },
  },
  {
    field: 'queryResult',
    label: '查询结果',
    component: 'Select',
    componentProps: {
      options: [
        { label: '有下载清单', value: 1 },
        { label: '无下载清单', value: 0 },
      ],
      placeholder: '请选择查询结果',
      allowClear: true,
    },
    colProps: { span: 6 },
  },
  {
    field: 'queryMethod',
    label: '查询方式',
    component: 'Select',
    componentProps: {
      options: [
        { label: '系统自动查询', value: 'auto' },
        { label: '人工手动查询', value: 'manual' },
      ],
      placeholder: '请选择查询方式',
      allowClear: true,
    },
    colProps: { span: 6 },
  },
];

// 立即查询表单配置
export const queryFormSchema: FormSchema[] = [
  {
    field: 'isSetType',
    label: '设定业务类型',
    component: 'Switch',
    defaultValue: false,
    colProps: { span: 24 },
  },
  {
    field: 'businessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      options: businessTypeOptions,
      placeholder: '请选择业务类型',
    },
    show: ({ values }) => values.isSetType,
    colProps: { span: 24 },
  },
];

// 文件详情表格列配置
export const detailsColumns: BasicColumn[] = [
  {
    title: '文件ID',
    dataIndex: 'fileId',
    width: 280,
    ellipsis: true,
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    width: 100,
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 120,
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: 180,
    customRender: ({ text }) => {
      const option = businessTypeOptions.find(item => item.value === text);
      return option ? option.label : text;
    },
  },
  {
    title: '文件时间',
    dataIndex: 'fileTime',
    width: 180,
  },
];
