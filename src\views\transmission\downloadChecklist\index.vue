<template>
  <div class="download-checklist">
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:search-outlined" @click="handleQuery">立即查询</a-button>
      </template>
      <template #queryResult="{ record }">
        <a-tag :color="record.queryResult === 1 ? 'success' : 'error'">
          {{ record.queryResult === 1 ? '有下载清单' : '无下载清单' }}
        </a-tag>
      </template>
      <template #resultDescription="{ record }">
        {{ record.queryResult === 1 ? '获取文件下载列表成功' : '没有文件可以下载' }}
      </template>
      <template #fileCount="{ record }">
        {{ record.queryResult === 1 ? record.fileCount : 0 }}
      </template>
      <template #fileList="{ record }">
        <a-button v-if="record.queryResult === 1" type="link" size="small" @click="handleViewDetails(record)"> 查看详情 </a-button>
        <span v-else>--</span>
      </template>
    </BasicTable>

    <!-- 立即查询对话框 -->
    <BasicModal @register="registerQueryModal" title="立即查询" :width="500" @ok="handleStartQuery">
      <BasicForm @register="registerQueryForm" />
    </BasicModal>

    <!-- 文件清单详情对话框 -->
    <BasicModal @register="registerDetailsModal" title="文件清单详情" :width="1200" :footer="null">
      <BasicTable
        @register="registerDetailsTable"
        class="file-details-table"
        :dataSource="fileDetailsData"
        :columns="detailsColumns"
        :pagination="false"
        :showIndexColumn="true"
        size="small"
        :bordered="true"
      />
    </BasicModal>
  </div>
</template>

<script lang="ts" name="download-checklist" setup>
  import { unref, ref } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { columns, searchFormSchema, queryFormSchema, detailsColumns } from './downloadChecklist.data';
  import { list, startQuery, getFileDetails } from './downloadChecklist.api';

  const { createMessage } = useMessage();

  // 文件详情数据
  const fileDetailsData = ref([]);

  // 立即查询模态框
  const [registerQueryModal, { openModal: openQueryModal, closeModal: closeQueryModal }] = useModal();

  // 文件详情模态框
  const [registerDetailsModal, { openModal: openDetailsModal }] = useModal();

  // 立即查询表单
  const [registerQueryForm, { validate: validateQueryForm, resetFields: resetQueryForm }] = useForm({
    schemas: queryFormSchema,
    labelWidth: 120,
    showActionButtonGroup: false,
  });

  // 文件详情表格
  const [registerDetailsTable] = useTable({
    columns: detailsColumns,
    dataSource: fileDetailsData,
    pagination: false,
    showIndexColumn: true,
    size: 'small',
    bordered: true,
  });

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'download-checklist-list',
    tableProps: {
      title: '文件下载清单',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      showActionColumn: false,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'download_checklist_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 100,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'queryTime', order: 'desc' }, params);
      },
    },
  });

  const [registerTable, { reload, getSelectRowKeys }] = tableContext;

  // 行选择配置
  const rowSelection = {
    type: 'checkbox',
    selectedRowKeys: unref(getSelectRowKeys),
    onChange: onSelectChange,
    onSelect: onSelect,
    onSelectAll: onSelectAll,
  };

  /**
   * 选择事件
   */
  function onSelectChange(selectedRowKeys: string[]) {
    getSelectRowKeys.value = selectedRowKeys;
  }

  function onSelect(_record: any, _selected: any, _selectedRows: any) {
    // 处理单行选择
  }

  function onSelectAll(_selected: any, _selectedRows: any, _changeRows: any) {
    // 处理全选
  }

  /**
   * 立即查询
   */
  function handleQuery() {
    resetQueryForm();
    openQueryModal();
  }

  /**
   * 发起查询
   */
  async function handleStartQuery() {
    try {
      const values = await validateQueryForm();

      // 显示加载状态
      const loading = createMessage.loading('正在查询...', 0);

      try {
        await startQuery(values);
        loading();
        createMessage.success('手动查询任务已发起！');
        closeQueryModal();
        // 刷新列表
        reload();
      } catch (error) {
        loading();
        createMessage.error('查询失败：' + error.message);
      }
    } catch (error) {
      console.error('表单验证失败', error);
    }
  }

  /**
   * 查看文件详情
   */
  async function handleViewDetails(record) {
    try {
      const fileDetails = await getFileDetails(record.id);
      // 更新详情表格数据
      fileDetailsData.value = fileDetails;
      openDetailsModal();
    } catch (error) {
      createMessage.error('获取文件详情失败：' + error.message);
    }
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
