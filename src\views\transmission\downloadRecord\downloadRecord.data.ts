import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '文件ID',
    dataIndex: 'fileId',
    width: 280,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '文件名称',
    dataIndex: 'fileName',
    width: 320,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '版本号',
    dataIndex: 'version',
    width: 100,
    align: 'center',
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    width: 120,
    align: 'right',
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    width: 150,
    customRender: ({ text }) => {
      const businessTypeMap = {
        'land': '土地',
        'house': '房屋',
        'advert': '广告位',
        'equipment': '设备',
        'other': '其他',
        'rent': '租赁',
        'rentAbort': '租赁中止',
        'rentAd': '招租公告',
        'rentDetail': '租金明细',
        'transfer': '转让',
        'vacant': '空置/闲置',
        'occupy': '占用',
        'lend': '借出',
        'self': '自用',
        'systemInfo': '制度信息',
        'rentOverview': '租金一览',
        'assetQRCode': '资产二维码'
      };
      return businessTypeMap[text] || text || '-';
    },
  },
  {
    title: '下载时间',
    dataIndex: 'downloadTime',
    width: 180,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '文件名称',
    field: 'fileName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入文件名称',
    },
  },
  {
    label: '业务类型',
    field: 'businessTypes',
    component: 'Select',
    componentProps: {
      placeholder: '请选择业务类型',
      mode: 'multiple',
      options: [
        { label: '土地', value: 'land' },
        { label: '房屋', value: 'house' },
        { label: '广告位', value: 'advert' },
        { label: '设备', value: 'equipment' },
        { label: '其他', value: 'other' },
        { label: '租赁', value: 'rent' },
        { label: '租赁中止', value: 'rentAbort' },
        { label: '招租公告', value: 'rentAd' },
        { label: '租金明细', value: 'rentDetail' },
        { label: '转让', value: 'transfer' },
        { label: '空置/闲置', value: 'vacant' },
        { label: '占用', value: 'occupy' },
        { label: '借出', value: 'lend' },
        { label: '自用', value: 'self' },
        { label: '制度信息', value: 'systemInfo' },
        { label: '租金一览', value: 'rentOverview' },
        { label: '资产二维码', value: 'assetQRCode' },
      ],
    },
  },
  {
    label: '下载时间',
    field: 'downloadTimeRange',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
