<template>
  <div>
    <BasicTable @register="registerTable">
    </BasicTable>
  </div>
</template>

<script lang="ts" name="download-record-list" setup>
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema } from './downloadRecord.data';
  import { list } from './downloadRecord.api';

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'download-record-list',
    tableProps: {
      title: '',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: false,
      // 显示表格设置
      showTableSetting: true,
      showActionColumn: false,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'download_record_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 100,
        // 显示展开/收起按钮
        showAdvancedButton: false,
        // 超过3列时默认折叠
        autoAdvancedCol: 3,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      beforeFetch: (params) => {
        return Object.assign({ column: 'downloadTime', order: 'desc' }, params);
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable] = tableContext;
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
